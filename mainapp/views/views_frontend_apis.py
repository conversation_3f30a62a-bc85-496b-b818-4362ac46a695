import datetime
import hashlib
import html
import io
import json
import logging
import os
import random
import re
import uuid
import copy
import math
from typing import List, Dict, Callable
from urllib.parse import urlparse
from zoneinfo import ZoneInfo
from deep_translator import GoogleTranslator

import deep_translator
import markdown
import redis
import requests
import stripe
import tldextract
from requests.auth import HTTPBasicAuth
from bs4 import BeautifulSoup
from collections import OrderedDict
from cryptography.fernet import Fernet, InvalidToken
from django.contrib.auth.hashers import make_password
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.db import transaction, IntegrityError
from django.db.models import (Count, QuerySet, OuterRef, Subquery, Prefetch, Q, F,
                              When, Value, Case, IntegerField, Sum)
from django.http import JsonResponse
from django.core.paginator import Paginator
from django.utils import timezone
from django.http import HttpResponseRedirect
from docx import Document
from htmldocx import HtmlToDocx
from langchain.output_parsers import PydanticOutputParser, RetryWithErrorOutputParser
from langchain.prompts import PromptTemplate
from pydantic import BaseModel, Field
from langchain.schema import OutputParserException
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.request import Request
from rest_framework.response import Response
from google_auth_oauthlib.flow import Flow
from googleapiclient.http import HttpError
from google.auth.exceptions import RefreshError
from google.oauth2.credentials import Credentials

from AbunDRFBackend.settings import (ADIL_EMAIL, REDIS_HOST, REDIS_PORT, REDIS_TASK_DATA_DB, RESET_PASSWORD_ENCRYPTION_KEY, RESET_PASSWORD_EXPIRY_HOURS,
                                     ABUN_NOTIFICATION_EMAIL, ABUN_INFO_EMAIL, RESET_PASSWORD_LINK_DOMAIN, STRIPE_CUSTOMER_PORTAL_RETURN_URL, JD_EMAIL, AMIN_EMAIL, EMAIL_VERIFICATION_ENCRYPTION_KEY, WP_RETURN_URL_DOMAIN, WP_DOMAIN_ENCRYPTION_KEY, MINI_AI_TOOL_OPENAI_API_KEY, DEBUG,
                                     FLY_ARTICLE_GEN_APP_NAME, FLY_ARTICLE_GEN_DEPLOY_TOKEN, FLY_WEBSITE_SCANNING_APP_NAME, FLY_WEBSITE_SCANNING_DEPLOY_TOKEN, GHL_CLIENT_ID, GHL_CLIENT_SECRET)
from mainapp import google_integration_utils
from mainapp.article_title_gen_v2.titles_gen_v2 import ArticleTitleGeneratorV2
from mainapp.chatgpt_prompts import *

from mainapp.email_messages import (reset_password_email_body, contact_us_email_body, contact_us_ack_email_body,
                                    unable_to_publish_article_body, feature_request_email_body)
from mainapp.json_responses import JsonResponseBadRequest, JsonResponseKeyError, JsonResponseServerError, JsonResponse404, JsonResponseGone, JsonResponseRedirect
from mainapp.models import (User, Website, KubernetesJob, Keyword, Article, IgnoredCompetitor, HypestatData, Competitor, BlockWebsiteKeywords,
                            KeywordProject, ScheduleArticlePosting, BlockKeywords, AutomationProject, BackLink, Survey, WordpressCategories,
                            ProgrammaticSeoTitle, AppSumoLicense, BlogFinder, BlogFinderProject, GlossaryTopic, GlossaryContent,
                            ScheduleGlossaryPosting, GuestPostFinderQuery, RedditPostFinderQuery, RedditPostFinderResult, GSCKeywordStatus,
                            WebPage, GhostIntegration, InstructionAndContext, AICalculator, AIStreamingToken, GHLIntegration, GHLCategories,
                            AICalculatorVersion, WordpressPublishedArticle, ArticleGenerationQueue, WebsiteIndexation)
from mainapp.serializers import (ArticleTitleTableDataSerializer, KeywordDetailsTableDataSerializer, WordpressCategoriesSerializer,
                                 BackLinkSerializer, WebflowSitesSerializer,WordpressSitesSerializer, WixSitesSerializer,
                                 AutomationProjectSerializer, ShopifyShopsSerializer, WebPageSerializer, BlogFinderSerializer, BlogFinderProjectSerializer,
                                 GlossaryContentSerializer, GlossaryTopicSerializer, GuestPostFinderQuerySerializer, GuestPostFinderResultSerializer,
                                 RedditPostFinderQuerySerializer, RedditPostFinderResultSerializer, GhostSitesSerializer, AICalculatorSerializer, GHLSitesSerializer,GHLCategoriesSerializer, GhlSaveCategoriesSerializer, WordpressPublishedArticleSerializer)
from mainapp.stripe_utils import (generate_stripe_event_idempotency_key, get_stripe_product_data, get_stripe_product_data_by_id,
                                  create_checkout_session, get_all_product_data)
from mainapp.tasks import *
from mainapp.utils import *
from mainapp.utils import add_calculator_branding, remove_calculator_branding
from mainapp.decorators import cors_allow_all

from decimal import Decimal, ROUND_HALF_UP

from django.core.serializers import serialize

from mainapp.custom_featured_images import generate_custom_feature_image
from mainapp.article_title_gen_v2.serper_scraper4_3 import generate_titles_from_serper
from mainapp.chroma_db_manager import ChromaDBManager
from mainapp.website_scanning import WebsiteScanning
from mainapp.competitor_finder import CompetitorFinder
from mainapp.quickindex.indexing import index_url

from celery.result import AsyncResult
from celery_progress.backend import Progress
from celery import current_app

logger = logging.getLogger('abun.frontend.apis')
import traceback

USER_AGENTS = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Edge/91.0.864.59",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (Linux; Android 11; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36",
                "Mozilla/5.0 (Linux; Android 10; SM-A505F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/******** Firefox/89.0",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7; rv:89.0) Gecko/******** Firefox/89.0",
                "Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:89.0) Gecko/******** Firefox/89.0",
                "Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
                "Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1",
            ]

@api_view(['POST'])
@transaction.atomic
def email_verification_api(request: Request):
    """
    Marks user's account email id as verified.

    [This does not require user to be logged in]

    :param request: Django Rest Framework's Request object.
    """
    if request.method == 'POST':
        try:
            encrypted_email: bytes = request.data['token'].encode('utf-8')
            logger.debug(encrypted_email)
        except KeyError:
            return JsonResponseBadRequest()

        f = Fernet(EMAIL_VERIFICATION_ENCRYPTION_KEY)

        try:
            email: str = f.decrypt(encrypted_email).decode('utf-8')
            user: User = User.objects.get(email=email)
        except InvalidToken:
            logger.error(f"email_verification_api - Invalid token.")
            return JsonResponseBadRequest()
        except User.DoesNotExist:
            logger.error(f"email_verification_api - Account with email id {email} does not exists.")
            return JsonResponseBadRequest()

        user.verified = True
        user.save()

        # Start scanning the website
        website: Website = user.current_active_website

        if website and not website.is_crawled and not DEBUG:
            # Fetch the website sitemaps and Start crawling the pages
            celery_start_website_scanning.delay(website.domain, run='generate-summary')

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def switch_active_website_api(request: Request):
    """
    Used for switch user's active website to a different one. Frontend should refresh the page once it gets status 200.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            domain: str = request.data['domain']
        except KeyError:
            return JsonResponseBadRequest()

        try:
            new_active_website = user.website_set.get(domain=domain)
        except Website.DoesNotExist:
            return JsonResponseBadRequest()

        user.current_active_website = new_active_website
        user.save()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@transaction.atomic
def forgot_password_api(request: Request):
    """
    Prepares user account for password reset, generates and mails the link to user.

    [This does not require user to be logged in]

    :param request: Django Rest Framework's Request object.
    """
    if request.method == 'POST':
        try:
            email: str = request.data['email']
        except KeyError:
            return JsonResponseBadRequest()

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return JsonResponseBadRequest()

        f = Fernet(RESET_PASSWORD_ENCRYPTION_KEY)
        encrypted_email: bytes = f.encrypt(email.encode('utf-8'))
        reset_request_uid: str = uuid.uuid4().hex
        expiry = datetime.datetime.now(tz=ZoneInfo('UTC')) + datetime.timedelta(hours=RESET_PASSWORD_EXPIRY_HOURS)
        reset_link = f"{RESET_PASSWORD_LINK_DOMAIN}/reset-password/{encrypted_email.decode('utf-8')}/{reset_request_uid}"

        user.reset_password_uid = reset_request_uid
        user.reset_password_expiry = expiry
        user.save()

        send_email(user.email,
                   ABUN_NOTIFICATION_EMAIL,
                   "Team Abun",
                   "Your Password Reset Request on Abun",
                   reset_password_email_body(user.username, reset_link, RESET_PASSWORD_EXPIRY_HOURS))

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@transaction.atomic
def reset_password_api(request: Request):
    """
    Used for resetting user password. Body data should contain encrypted email and reset UID.

    We add the new password to user model after checking certain conditions and remove the reset uid
    and expiry values so that the link cannot be be used again.

    [This does not require user to be logged in]

    :param request: Django Rest Framework's Request object.
    """
    if request.method == 'POST':
        try:
            encrypted_email: bytes = request.data['encrypted_email'].encode('utf-8')
            reset_password_uid: str = str(request.data['reset_password_uid'])
            new_password: str = html.escape(request.data['new_password'])
        except KeyError:
            return JsonResponseBadRequest()

        except AttributeError:
            logger.error(f"Reset Password: Cannot encode non-string value {request.data['encrypted_email']}")
            return JsonResponseBadRequest()

        if len(new_password) < 6:
            logger.error(f"Reset Password: Password length is less than 6")
            return JsonResponseBadRequest()

        f = Fernet(RESET_PASSWORD_ENCRYPTION_KEY)
        email = f.decrypt(encrypted_email).decode('utf-8')

        user = User.objects.get(email=email)

        if (user.reset_password_uid == reset_password_uid) and \
                user.reset_password_expiry and \
                (user.reset_password_expiry >= datetime.datetime.now(tz=ZoneInfo('UTC'))):

            user.reset_password_uid = None
            user.reset_password_expiry = None
            user.password = make_password(new_password)
            user.save()

            return JsonResponse(status=200, data={'message': "OK"})

        else:
            return JsonResponse(status=403, data={'message': "Forbidden"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def contact_us_api(request: Request):
    """
    Sends user email message to us. Requires message subject, body and optionally any attachment files

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            subject: str = request.data['subject']
            message: str = request.data['message']
        except KeyError:
            return JsonResponseBadRequest()

        filelist: List[InMemoryUploadedFile] = []
        for filename in request.FILES:
            filelist.append(request.FILES[filename])

        # ------------------ Send an email to us ------------------
        email_content: str = contact_us_email_body(user.username, user.email, message)
        # send_email("<EMAIL>",
        #            ABUN_NOTIFICATION_EMAIL,
        #            "Team Abun",
        #            subject,
        #            email_content,
        #            filelist)
        send_email(JD_EMAIL,
                   ABUN_NOTIFICATION_EMAIL,
                   "Team Abun",
                   subject,
                   email_content,
                   filelist)
        send_email(AMIN_EMAIL,
                   ABUN_NOTIFICATION_EMAIL,
                   "Team Abun",
                   subject,
                   email_content,
                   filelist)

        # ------------------ Send acknowledgement email to user ------------------
        ack_email_content: str = contact_us_ack_email_body(user.username)
        send_email(user.email,
                   ABUN_NOTIFICATION_EMAIL,
                   "Team Abun",
                   "Query/Resolution",
                   ack_email_content,
                   filelist)

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_keywords_api(request: Request):
    """
    Fetches list of competitors for website using ChatGPT. Returns List of {"name", "url"}.

    :param request: Django Rest Framework's Request object
    """
    if request.method == "GET":
        try:
            kewyords: List[str] = request.data['keywords']
        except KeyError:
            return JsonResponseBadRequest({'err_id': "Missing 'keywords' data"})

        # Sanitize user inputs
        for index, kw in enumerate(kewyords):
            kewyords[index] = html.escape(kw).strip()

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def competitors_api(request: Request):
    """
    Fetches list of competitors for website using ChatGPT. Returns List of {"name", "url"}.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    class Competitors(BaseModel):
        competitors: List[str] = Field(description="top level domain of competitor")

    if request.method == "GET":
        try:
            protocol: str = request.query_params['protocol']
            domain: str = request.query_params['domain']
        except KeyError:
            return JsonResponseBadRequest()

        llm = ChatOpenAI(
            model_name=get_gpt_model_name(),
            temperature=0.7
        )

        parser = PydanticOutputParser(pydantic_object=Competitors)
        retry_parser = RetryWithErrorOutputParser.from_llm(parser=parser, llm=llm)
        prompt = PromptTemplate(
            template=get_competitors_prompt(),
            input_variables=["protocol", "domain"],
            partial_variables={"format_instructions": parser.get_format_instructions()}
        )

        _input = prompt.format_prompt(protocol=protocol, domain=domain)
        _response = llm.invoke(_input.to_string())
        _output = _response.content

        total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
        add_or_update_gpt_token_usage(total_tokens)

        try:
            structured_output = parser.parse(_output)
        except OutputParserException:
            logger.debug("Competitors output parser failed. Retrying...")
            structured_output = retry_parser.parse_with_prompt(_output, _input)

        # valid_competitors: List[Dict] = get_valid_competitors(structured_output.competitors)

        # Sort the results with descending keyword count and get the first 10
        # valid_competitors = sorted(valid_competitors, key=lambda comp: comp['keyword_count'], reverse=True)[:10]

        # return JsonResponse(status=200, data=valid_competitors, safe=False)
        return JsonResponse(status=200, data=structured_output.competitors, safe=False)

    else:
        return JsonResponseBadRequest()


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def connect_website_api(request: Request):
    """
    Connects website to Abun and starts content plan geenration.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    try:
        current_plan_data = get_stripe_product_data(user)
        current_plan_name = current_plan_data['name']
    except stripe.error.InvalidRequestError:
        current_plan_data = None
        current_plan_name = 'Trial'

    if not current_plan_data:
        return JsonResponseBadRequest(additional_data={"err_id": "PLAN_NOT_FOUND"})

    # if current_plan_name == 'Trial':
    #     return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    if request.method == 'GET':
        website_limit: int = current_plan_data['metadata']['websites']

        if user.website_set.count() >= website_limit:
            return JsonResponse(status=302, data={'redirect_to': "max_websites"})

        # fetch required data and send back
        return JsonResponse(status=200, data={
            'username': user.username,
        })

    elif request.method == 'POST':
        try:
            domain: str = html.escape(request.data['domain'].strip().lower())
            protocol: str = html.escape(request.data['protocol'].strip().lower())
            blog_url: str = html.escape(request.data['blog'].strip().lower())
            title: str = html.escape(request.data['title'].strip()).replace("&amp;", "&")
            description: str = html.escape(request.data['description'].strip()).replace("&amp;", "&")
            industry: str = html.escape(request.data['industry'].strip()).replace("&amp;", "&")
            icp_text: str = html.escape(request.data['icp'].strip()).replace("&amp;", "&")
            keywords: List[str] = request.data.get('keywords', [])  # list of keywords
            competitors: List[str] = request.data['competitors']  # list of domains
            # generate_content_plan: bool = request.data.get('generate_content_plan', True)
        except KeyError as k:
            return JsonResponseBadRequest({'message': f"Missing {k} in api request."})

        logger.debug(domain)
        logger.debug(protocol)
        logger.debug(blog_url)
        logger.debug(title)
        logger.debug(description)
        logger.debug(industry)
        logger.debug(icp_text)
        logger.debug(keywords)
        logger.debug(competitors)

        # sanitize keywords data
        for index, kw in enumerate(keywords):
            keywords[index] = html.escape(kw).strip().lower().replace("&amp;", "&")

        # Get website name
        try:
            res = requests.get(f"https://autocomplete.clearbit.com/v1/companies/suggest?query={domain}")
            data = res.json()
            if data:
                website_name: str = data[0]['name']
            else:
                website_name: str = domain.split(".")[-2].capitalize()
        except Exception as err:
            logger.error(f"Clearbit API error: {err}")
            website_name: str = domain.split(".")[-2].capitalize()

        # create website
        try:
            # we'll get logo url and color from later k8 task
            website = Website(
                user=user,
                domain=domain,
                name=website_name,
                protocol=protocol,
                title=title,
                description=description,
                industry=industry,
                icp_text=icp_text,
            )
            website.save()

            # Log the event
            add_website_log(
                user=user,
                domain=domain,
                message=f"Website instance created for {domain}",
            )

        except IntegrityError as err:
            logger.error(f"InegrityError while adding website {domain} for {user.email}: {err}")
            return JsonResponseBadRequest({'err_id': 'WEBSITE_ALREADY_CONNECTED'})

        # migrate all the data from default website to this one and delete the default website
        active_website: Website = user.current_active_website
        if active_website and active_website.domain.startswith("default-") and active_website.domain.endswith(".xyz"):
            # Update model relations to point to new website instead
            Article.objects.filter(website=active_website).update(website=website)
            Keyword.objects.filter(website=active_website).update(website=website)
            KeywordProject.objects.filter(website=active_website).update(website=website)
            AutomationProject.objects.filter(website=active_website).update(website=website)
            ProgrammaticSeoTitle.objects.filter(website=active_website).update(website=website)

            # Copy website settings
            website.keyword_strategy = active_website.keyword_strategy
            website.image_source = active_website.image_source
            website.feature_image_template_id = active_website.feature_image_template_id
            website.feature_image_required = active_website.feature_image_required
            website.generate_bannerbear_featured_image = active_website.generate_bannerbear_featured_image
            website.article_tone_of_voice = active_website.article_tone_of_voice
            website.article_language_preference = active_website.article_language_preference
            website.external_backlinks_preference = active_website.external_backlinks_preference
            website.max_internal_backlinks = active_website.max_internal_backlinks
            website.max_external_backlinks = active_website.max_external_backlinks
            website.ai_generated_image_style = active_website.ai_generated_image_style
            website.images_file_format = active_website.images_file_format

            # Update Integration relations
            WordpressIntegration.objects.filter(website=active_website).update(website=website)
            WebflowIntegration.objects.filter(website=active_website).update(website=website)
            WixIntegration.objects.filter(website=active_website).update(website=website)
            ShopifyIntegration.objects.filter(website=active_website).update(website=website)
            WordpressIntegration.objects.filter(website=active_website).update(website=website)

            # Delete the default website
            active_website.delete()

        if user.has_ltd_plans:
            website.feature_image_template_id = "yKBqAzZ9xwB0bvMx36"

        user.current_active_website = website
        user.save()

        # Save logos for each blacklist domain
        website.save()

        # Fetch and save hypestat data for these competitors
        celery_save_hypestat_data.delay(competitors)
        website.save()

        # Make a get request to check if the logo url is exists or not
        logo_url = f"https://logo.clearbit.com/{domain}"

        try:
            res = requests.get(logo_url, timeout=30)

            if res.ok:
                website.logo_url = logo_url
                website.save()
            else:
                logger.info(f"Getting a non-success status code for '{logo_url}'. Status code: {res.status_code}")

        except Exception as err:
            # default logo wil be use in this case
            logger.error(f"Error while fetching logo for {domain}: {err}")

        # ======================================
        # ------------ content plan ------------
        # ======================================
        # if generate_content_plan:
        #     with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        #         content_plan_job_id = generate_k8_job_id("contentplan", domain)

        #         content_plan_data = {
        #             "domain": domain,
        #             "protocol": protocol,
        #             "industry": industry,
        #             "icp_text": icp_text,
        #             "user_keywords": keywords,
        #             "competitors": competitors,
        #             "abun_webhook_url": reverse("wh-k8-content-plan"),
        #         }

        #         website.content_plan_task_data = content_plan_data
        #         website.content_plan_task_progress = 30
        #         website.save()

        #         content_plan_redis_key: str = (
        #             website.domain.replace(".", "") + "-contentplan-task-data"
        #         )
        #         redis_connection.set(content_plan_redis_key, json.dumps(content_plan_data))
        #         redis_connection.expire(content_plan_redis_key, REDIS_CONTENT_PLAN_EXPIRY)

        #         content_plan_k8_job = KubernetesJob(
        #             job_id=content_plan_job_id,
        #             user=user,
        #             website=website,
        #             status="running"
        #         )
        #         content_plan_k8_job.save()
        #         create_k8_job(
        #             content_plan_job_id,
        #             "content_plan",
        #             content_plan_job_id,
        #             user.id,
        #             [content_plan_job_id, content_plan_redis_key]
        #         )

        # --------------------- Create and add competitors ---------------------
        competitors_objects = []

        for domain in competitors:
            competitors_objects.append(
                Competitor(
                    website=website,
                    domain=domain,
                    protocol="https",
                    logo_url=f""
                )
            )

        Competitor.objects.bulk_create(competitors_objects)
        website.save()

        if (current_plan_name != 'Trial' or user.verified) and not DEBUG:
            # Fetch the website sitemaps and Start crawling the pages
            celery_start_website_scanning.delay(domain, run='generate-summary')

        return JsonResponse(status=200, data={'message': "Website connected. Content Plan generation started!"})

    else:
        return JsonResponseBadRequest()


# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# def content_plan_done_api(request: Request):
#     """
#     Returns json response with key 'done' (boolean) if content plan generation is in 'done' state.

#     :param request: Django Rest Framework's Request object
#     """
#     user: User = request.user
#     website: Website = user.current_active_website

#     if request.method == 'GET':
#         if website:
#             return JsonResponse(
#                 status=200,
#                 data={
#                     "status": website.content_plan_generation_status,
#                     "progress": website.content_plan_task_progress
#                 },
#             )
#         else:
#             return JsonResponseBadRequest({'err_id': "NO_WEBSITE"})

#     else:
#         return JsonResponseBadRequest()


# @api_view(['POST'])
# @permission_classes([IsAuthenticated])
# @transaction.atomic
# def retry_content_plan_api(request: Request):
#     """
#     Retries content plan generation.

#     :param request: Django Rest Framework's Request object
#     """
#     user: User = request.user
#     website: Website = user.current_active_website

#     if request.method == 'POST':
#         if website.content_plan_generation_status == 'processing':
#             return JsonResponseBadRequest({'err_id': "CONTENT_PLAN_ALREADY_PROCESSING"})

#         if website.content_plan_generation_status == 'done':
#             return JsonResponseBadRequest({'err_id': "CONTENT_PLAN_ALREADY_DONE"})

#         # Generate the new job id and create redis key using that.
#         content_plan_job_id = generate_k8_job_id('contentplan', website.domain)
#         content_plan_redis_key: str = content_plan_job_id + "-task-data"

#         # Fetch the content plan data. If not available for any reason, send 410 response.
#         task_data: Dict = website.content_plan_task_data
#         if not task_data:
#             logger.error("admin_retry_content_plan() - Data required for retrying content plan is missing.")
#             return JsonResponseGone(additional_data={'err_id': "TASK_DATA_MISSING"})

#         # Add this to redis task data db
#         with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
#             redis_connection.set(content_plan_redis_key, json.dumps(task_data))
#             redis_connection.expire(content_plan_redis_key, REDIS_CONTENT_PLAN_EXPIRY)

#         content_plan_k8_job = KubernetesJob(
#             job_id=content_plan_job_id,
#             user=user,
#             website=website,
#             status='running'
#         )
#         content_plan_k8_job.save()
#         create_k8_job(
#             content_plan_job_id,
#             'content_plan',
#             content_plan_job_id,
#             user.id,
#             [content_plan_job_id, content_plan_redis_key]
#         )

#         # Save job details to website
#         website.content_plan_generation_status = 'processing'
#         website.content_plan_started_on = datetime.datetime.now(tz=ZoneInfo('UTC'))
#         website.save()

#         return JsonResponse(status=200, data={'message': "OK"})

#     else:
#         return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_domain_api(request: Request):
    """
    Checks if domain is in correct format and valid.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    # Check limit only if current_acive_website is a default website
    if website and not website.domain.startswith("default-") and not website.domain.endswith(".xyz"):
        try:
            current_plan = get_stripe_product_data(user)
            website_limit: int = current_plan['metadata']['websites']

            if user.website_set.count() >= website_limit:
                domain_details = {
                    'domain': '',
                    'protocol': '',
                    'valid': True,
                    'already_connected': False,
                    'blocked_domain': False,
                    'website_limit_exceed': True
                }
                return JsonResponse(status=200, data=domain_details)

        except stripe.error.InvalidRequestError:
            return JsonResponseBadRequest()

    if request.method == "GET":
        try:
            domain: str = request.query_params['domain']
        except KeyError:
            return JsonResponseBadRequest()

        domain = domain.replace("www.", "")

        if ("https://" not in domain) and ("http://" not in domain):
            domain = "https://" + domain

        domain_details = {
            'domain': '',
            'protocol': '',
            'valid': True,
            'already_connected': False,
            'blocked_domain': False,
            'website_limit_exceed': False
        }

        # check if both domain and prefix are present
        tld_data = tldextract.extract(domain)
        if not (tld_data.domain and tld_data.suffix):
            domain_details['valid'] = False

        # if not parse_result.netloc:
        #     domain_details['valid'] = False

        parse_result = urlparse(domain)
        domain_details['protocol'] = parse_result.scheme
        domain_details['domain'] = parse_result.netloc

        if Website.objects.filter(domain=parse_result.netloc.lower()).exists():
            domain_details['already_connected'] = True

        blocked_keywords = BlockWebsiteKeywords.objects.all().values_list('keyword', flat=True)
        for keyword in blocked_keywords:
            if keyword in domain:
                domain_details['blocked_domain'] = True
                break

        logger.debug(domain_details)

        return JsonResponse(status=200, data=domain_details)

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def website_title_description_api(request: Request):
    """
    Fetches title, description and few other pieces of information for given 'domain'

    :param request: Django Rest Framework's Request object
    """

    def meta_desc_filter(meta):
        return (('name' in meta.attrs) and (meta.attrs['name'] in ['description', 'twitter:description'])) or \
            (('property' in meta.attrs) and (meta.attrs['property'] in ['og:description']))

    if request.method == "GET":
        try:
            protocol: str = request.query_params['protocol']
            domain: str = request.query_params['domain']
        except KeyError:
            return JsonResponseBadRequest()

        site_info = {
            'title': '',
            'description': '',
            'language': '',
        }

        url: str = protocol + "://" + domain

        try:
            res = requests.get(url, timeout=10)
            if res.status_code == 200:
                try:
                    soup = BeautifulSoup(res.text, features='html.parser')
                    # language
                    try:
                        site_info['language'] = soup.find('html')['lang']
                    except KeyError:
                        logger.debug(f"{url} - lang attribute is missing")
                    # title
                    site_info['title'] = soup.title.text.encode('ascii', 'ignore').decode().strip()
                    # description
                    metas = soup.find_all('meta')
                    description = ""
                    for m in filter(meta_desc_filter, metas):
                        content = m.attrs['content'].encode('ascii', 'ignore').decode().strip()
                        if len(content) > len(description):
                            description = content
                    site_info['description'] = description
                except Exception as err:
                    logger.error(err)

        except Exception as err:
            logger.error(f"{err}")
            logger.error(f"Could not fetch website {url} for title & description...")

        # In case we don't get title & description for any reason, get them from data.similarweb.com API
        if not site_info['title']:
            try:
                logger.debug(f"Could not find title for {domain}. Fetching from API...")
                res = requests.get(f"https://data.similarweb.com/api/v1/data?domain={domain}", timeout=10)
                if res.status_code == 200:
                    res_json = res.json()
                    if ('Title' in res_json) and res_json['Title']:
                        site_info['title'] = res.json()['Title']
            except requests.exceptions.ConnectionError:
                logger.error("similarweb api connection error")
            except requests.exceptions.Timeout:
                logger.error("similarweb api timeout")

        if not site_info['description']:
            try:
                logger.debug(f"Could not find description for {domain}. Fetching from API...")
                res = requests.get(f"https://data.similarweb.com/api/v1/data?domain={domain}", timeout=10)
                if res.status_code == 200:
                    res_json = res.json()
                    if ('Description' in res_json) and res_json['Description']:
                        site_info['description'] = res.json()['Description']
            except requests.exceptions.ConnectionError:
                logger.error("similarweb api connection error")
            except requests.exceptions.Timeout:
                logger.error("similarweb api timeout")

        return JsonResponse(status=200, data=site_info)

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def website_industry_icp_api(request: Request):
    """
    Fetches ICP text for website

    :param request: Django Rest Framework's Request object
    """
    if request.method == 'GET':
        try:
            domain: str = html.escape(request.query_params['domain'].strip().lower())
            title: str = html.escape(request.query_params['title'].strip().lower())
            description: str = html.escape(request.query_params['description'].strip().lower())
        except KeyError as k:
            return JsonResponseBadRequest({'message': f"Missing {k} in request query params"})
        except Exception as err:
            logger.critical(err)
            return JsonResponseServerError()

        if not (domain or title or description):
            return JsonResponseBadRequest({'message': "One of the query param value is bad."})

        structured_output = get_industry_icp_llm_response(domain, title, description)

        logger.debug({
            'industry': structured_output.industry,
            'icp': structured_output.icp
        })

        # success!
        return JsonResponse(status=200, data={'industry': structured_output.industry,
                                              'icp': structured_output.icp})

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def article_titles_api(request: Request):
    """
    Returns paginated data for current website's article titles.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    # Get pagination and filter parameters
    page_raw = request.GET.get('page')
    per_page_raw = request.GET.get('per_page')
    page = int(page_raw) if page_raw and page_raw.isdigit() else 1
    per_page = int(per_page_raw) if per_page_raw and per_page_raw.isdigit() else 10
    tab = request.GET.get('tab', 'Generated Articles')
    search = request.GET.get('search', '')
    sort = request.GET.get('sort', '')

    # Base queryset with optimizations
    base_queryset = (user.articles
        .select_related('keyword', 'schedulearticleposting')
        .prefetch_related('keyword__keywordproject_set')
        .exclude(article_uid__startswith='wp-')
        .annotate(
            keyword_project_id=Subquery(
                KeywordProject.objects
                .filter(
                    website=OuterRef('website'),
                    keywords=OuterRef('keyword_id')
                )
                .values('project_id')[:1]
            )
        )
    )

    # Apply filters based on tab
    if tab == "Generated Articles":
        queryset = base_queryset.filter(
            is_archived=False,
            is_posted=False
        ).filter(
            Q(is_generated=True) | Q(is_processing=True) | Q(is_failed=True)
        )
    elif tab == "Published Articles":
        queryset = base_queryset.filter(is_posted=True, is_archived=False)
    elif tab == "Archived Articles":
        queryset = base_queryset.filter(is_archived=True)
    elif tab == "Scheduled Articles":
        queryset = base_queryset.filter(
            schedulearticleposting__isnull=False,
            is_archived=False
        )
    else:
        queryset = base_queryset

    # Apply search if provided
    if search:
        queryset = queryset.filter(
            Q(title__icontains=search) |
            Q(keyword__keyword__icontains=search)
        )

    # Apply sorting
    if sort:
        sort_fields = []
        for sort_item in sort.split(','):
            field, direction = sort_item.split(':')
            field_mapping = {
                'articleTitle': 'article_title',
                'keyword': 'keyword__keyword',
                'wordCount': 'word_count',
                'date': 'created_on',
                'generatedOn': 'generated_on',
                'postedOn': 'posted_on',
                'scheduledOn': 'schedulearticleposting__schedule_on'
            }
            db_field = field_mapping.get(field, field)
            if direction == 'desc':
                db_field = f'-{db_field}'
            sort_fields.append(db_field)

        if sort_fields:
            queryset = queryset.order_by(*sort_fields)
    else:
        # Default sorting
        if tab == "Scheduled Articles":
            queryset = queryset.order_by('-schedulearticleposting__schedule_on')
        elif tab == "Published Articles":
            queryset = queryset.order_by(F('posted_on').desc(nulls_last=True))
        else:
            queryset = queryset.annotate(
                custom_order=Case(
                    When(is_processing=True, then=Value(0)),
                    default=Value(1),
                    output_field=IntegerField(),
                )
            ).order_by('custom_order', F('generated_on').desc(nulls_last=True))

    # Get total count before pagination
    total_count = queryset.count()

    # Apply pagination
    start = (page - 1) * per_page
    end = start + per_page
    paginated_queryset = queryset[start:end]

    response_data = {
        'title_data': ArticleTitleTableDataSerializer(paginated_queryset, many=True).data,
        'total': total_count
    }

    return JsonResponse(response_data, status=200)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def archive_article_titles_api(request: Request):
    """
    Returns data for current website's archive article titles.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website = user.current_active_website

    if request.method == 'GET':
        if website:
            title_data = ArticleTitleTableDataSerializer(
                website.article_set.archive_queryset().select_related('keyword'), many=True
            ).data
            return JsonResponse(status=200, data={
                'title_data': title_data,
                'all_integrations': user.all_integrations,
            })
        else:
            return JsonResponse(status=200, data={
                'title_data': [],
                'integration_done': False
            })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def upload_keywords_api_v2(request: Request):
    """
    Uploads user keywords and fetches additional data for them.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            # Check if the user has reached the max limit for keywords
            max_keywords_allowed: int = get_stripe_product_data(user)['metadata']['max_keywords']
            if (user.keywords_generated) >= max_keywords_allowed:
                logger.error(f"upload_keywords_api_v2() - Max limit reached for user {user.email}")
                return JsonResponse(status=200, data={"status": "rejected", 'reason': "max_limit_reached", 'message': 'max limit reached'})
        except Exception as err:
            logger.error(f"upload_keywords_api_v2() - Error while checking max limit for user {user.email}: {err}")
            return JsonResponseServerError()

        try:
            keywords: List[str] = request.data['keywords']
            keywords = [html.escape(kw.strip()) for kw in keywords]  # clean & sanitize
            project_name: str = unescape_amp_char(html.escape(request.data['project_name'].strip()))
            selected_location = request.data['selectedLocation']
            keywords_uploaded_using = request.data['keywords_added_using']
        except KeyError:
            logger.error("Missing required data in request.")
            return JsonResponseBadRequest()

        if keywords_uploaded_using == "csv":
            try:
                current_plan_name = get_stripe_product_data(user)['name']
            except stripe.error.InvalidRequestError:
                current_plan_name = 'Trial'

            if current_plan_name == 'Trial':
                return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

        country: str = selected_location['country_iso_code'].lower()

        try:
            keywords = list(set(keywords)) # remove duplicates

            # filter out keywords that are too long
            # The maximum number of characters for each keyword: 80
            # The maximum number of words for each keyword phrase: 10
            keywords = [kw for kw in keywords if len(kw) <= 80 and len(kw.split()) <= 10]

            # Check keywords block list
            block_keywords = BlockKeywords.objects.all().values_list('keyword', flat=True)
            for keyword in keywords:
                if any(block_kw in keyword for block_kw in block_keywords):
                    return JsonResponse(status=200, data={
                        "status": "rejected",
                        "reason": "blocked_keyword_used",
                        "message": f"You cannot add '{keyword}' keyword. If you feel this is incorrect, please contact us via live chat."
                    })

            # ============================================================================
            # ----------------------------- ADD THE KEYWORDS -----------------------------
            # ============================================================================
            # if len(keywords) is less than 275 then we will use get_keyword_volume_data function
            # if len(keywords) is more than 275 and less than or equal to 1000 then we will use get_keyword_volume_data_v2 function
            # if len(keywords) is more than 1001 and less than or equal to 1275 then first 1000 keywords will be used with get_keyword_volume_data_v2 function
            # and remaining keywords will be used with get_keyword_volume_data function
            # and so on
            keywords_data_from_keywords_everywhere = []
            keyword_data_from_data_for_seo = []
            # Helper function to select the appropriate function
            def get_data_for_chunk(chunk):
                if len(chunk) <= 275:
                    return get_keyword_volume_data(chunk, country)
                else:
                    data = get_keyword_volume_data_v2(chunk, selected_location['location_code'])
                    if data is None:
                        data = []
                    return data

            # Process keywords in chunks
            chunk_size = 1000
            for i in range(0, len(keywords), chunk_size):
                chunk = keywords[i : i + chunk_size]
                if i + chunk_size > 1000 and i < 1000:
                    # Split chunk if it overlaps the 1000 keyword boundary
                    first_chunk = chunk[: 1000 - i]
                    second_chunk = chunk[1000 - i :]
                    keyword_data_from_data_for_seo += get_data_for_chunk(first_chunk)
                    keywords_data_from_keywords_everywhere += get_data_for_chunk(second_chunk)
                else:
                    if len(chunk) <= 275:
                        keywords_data_from_keywords_everywhere += get_keyword_volume_data(chunk, country)
                    else:
                        keyword_data_from_data_for_seo += get_keyword_volume_data_v2(chunk, selected_location['location_code'])
                        logger.debug(keyword_data_from_data_for_seo[0])

            # Remove duplicates
            keywords_data_from_keywords_everywhere = list({kw['keyword']: kw for kw in keywords_data_from_keywords_everywhere}.values())
            keyword_data_from_data_for_seo = list({kw['keyword']: kw for kw in keyword_data_from_data_for_seo}.values())

            # save all keywords to the database
            new_keywords_with_data = []

            for keyword_data in keywords_data_from_keywords_everywhere:
                cleaned_keyword: str = re.sub(r'[^\w\s]', "", keyword_data['keyword'])

                # if there is no data for the keyword, add it with default values
                if not keyword_data["vol"]:
                    keyword_obj = create_keyword_object(user, cleaned_keyword,
                                                        with_default_values=True,
                                                        country=country,
                                                        source="user-keyword")
                    new_keywords_with_data.append(keyword_obj)
                    continue

                cpc = keyword_data['cpc']['value']
                competition_index = keyword_data["competition"]
                formatted_cpc_value = Decimal(str(cpc)).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
                formatted_paid_difficulty = Decimal(str(competition_index)).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)

                keyword_obj = create_keyword_object(user, cleaned_keyword, **{
                    "source": "keywordseverywhere",
                    "country": country,
                    "serp_position": None,
                    "volume": keyword_data["vol"],
                    "cpc_currency": keyword_data['cpc']['currency'],
                    "cpc_value": formatted_cpc_value,
                    "paid_difficulty": formatted_paid_difficulty,
                    "trend": keyword_data["trend"]
                })
                new_keywords_with_data.append(keyword_obj)

            for data in keyword_data_from_data_for_seo:
                # cleaned_keyword: str = re.sub(r'[^a-zA-Z0-9\s]', "", keyword=data['keyword'])
                cleaned_keyword: str = re.sub(r'[^a-zA-Z0-9\s]', "", data['keyword'])

                # if there is no data for the keyword, add it with default values
                if data.get("search_volume", 0) == 0:
                    keyword_obj = create_keyword_object(user, cleaned_keyword,
                                                        with_default_values=True,
                                                        country=country,
                                                        source="user-keyword")
                    new_keywords_with_data.append(keyword_obj)
                    continue

                cpc = data.get("cpc", 1) if data.get("cpc") else 1
                competition_index = data.get("competition_index", 0) if data.get("competition_index") else 0
                formatted_cpc_value = Decimal(str(cpc)).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
                formatted_paid_difficulty = Decimal(str(competition_index)).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
                formatted_paid_difficulty = min(formatted_paid_difficulty, Decimal('99.99')) # cap the paid difficulty to 99.99

                keyword_obj = create_keyword_object(user, data["keyword"], **{
                    "source": "dataforseo",
                    "country": country,
                    "serp_position": None,
                    "volume": data.get("search_volume", 0) if data.get("search_volume") else 0,
                    "cpc_currency": "USD",
                    "cpc_value": formatted_cpc_value,
                    "paid_difficulty": formatted_paid_difficulty,
                    "trend": {'trends': data.get('trend', [])} if data.get('trend') else {'trends': []}
                })
                new_keywords_with_data.append(keyword_obj)

            # remove duplicates
            new_keywords_with_data = list({kw.keyword: kw for kw in new_keywords_with_data}.values())

            if (user.keywords_generated + len(new_keywords_with_data)) > max_keywords_allowed:
                # if the user has reached the limit, then only add as many keywords as possible
                new_keywords_with_data = new_keywords_with_data[:max_keywords_allowed - user.keywords_generated]

            # Helper function to validate and adjust the paid_difficulty field
            def validate_paid_difficulty(keyword_data):
                if keyword_data.paid_difficulty > Decimal('99.99'):
                    logger.error(f"paid_difficulty {keyword_data.paid_difficulty} exceeds the limit for keyword {keyword_data.keyword}. Capping to 99.99.")
                    keyword_data.paid_difficulty = Decimal('99.99')
                return keyword_data

            # Adjust your data before bulk creating
            validated_keywords_with_data = [validate_paid_difficulty(kw) for kw in new_keywords_with_data]

            # Create a new KeywordProject instance
            keyword_project = KeywordProject.objects.create(
                website=user.current_active_website,
                project_name=project_name,
                project_id=str(uuid.uuid4())[:16],
                location_iso_code=country.lower(),
            )

            # Wrap operations in a transaction
            with transaction.atomic():
                # Save all keywords to the database
                Keyword.objects.bulk_create(validated_keywords_with_data, ignore_conflicts=True)
                user.keywords_generated += len(validated_keywords_with_data)
                user.total_keywords_generated += len(validated_keywords_with_data)
                user.save()

                # Refresh from the database to ensure all existing keywords are included
                existing_keywords = Keyword.objects.filter(website=user.current_active_website,
                                                           keyword_md5_hash__in=[kw.keyword_md5_hash for kw in validated_keywords_with_data])

                # Add all new keywords to the keyword project
                keyword_project.keywords.add(*existing_keywords)

                # Update the total traffic volume
                total_traffic_volume = sum(kw.volume for kw in existing_keywords)
                keyword_project.total_traffic_volume = total_traffic_volume
                keyword_project.save()

        except Exception as err:
            logger.exception(err)
            return JsonResponseBadRequest()

        return JsonResponse(status=200, data={
            'message': "OK",
            'project_id': keyword_project.project_id
            # 'total_count': len(keywords),
            # 'connected_count': len(connected_keywords),
            # 'existing_count': len(existing_keywords),
            # 'new_count': len(new_keywords_with_data),
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def remove_keyword_project_keywords_api(request: Request):
    """
    Removes keyword from user website.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            keywords: list = [html.escape(keywordHash.strip()) for keywordHash in request.data['keywords']]
            keyword_project_id: str = html.escape(request.data['keyword_project_id'].strip())
        except KeyError as k:
            logger.error(f"Missing {k} in request data")
            return JsonResponseBadRequest(additional_data={'err_id': f"MISSING_{k}"})

        try:
            logger.debug(f"Removing keywords {keywords} from keyword project {keyword_project_id}")
            keyword_project = user.keyword_projects.get(project_id=keyword_project_id)
            for keywordHash in keywords:
                keyword_object = user.keywords.get(keyword_md5_hash=keywordHash)
                keyword_project.keywords.remove(keyword_object)
                keyword_project.total_traffic_volume -= keyword_object.volume
                keyword_project.save()

        except KeywordProject.DoesNotExist:
            return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_KEYWORD_PROJECT_FOUND"})
        except Keyword.DoesNotExist:
            return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_KEYWORD_FOUND"})
        except Exception as err:
            logger.exception(err)
            return JsonResponseBadRequest()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def generate_custom_title_for_keyword_api(request: Request):
    """
    Generates a custom title for a given keyword.

    :param request: Django Rest Framework's Request object
    """
    if request.method == "POST":
        user: User = request.user
        try:
            try:
                keyword: str = html.escape(request.data["keyword"].strip())
                keywordHash: str = html.escape(
                    request.data["keywordHash"].strip()
                )
                custom_article_title: str = unescape_amp_char(
                    html.escape(
                        request.data["customArticleTitle"].strip()
                    )
                )
            except KeyError:
                return JsonResponseBadRequest()

            try:
                keyword_object = user.keywords.get(
                    keyword_md5_hash=keywordHash, keyword=keyword
                )
            except Keyword.DoesNotExist:
                logger.error(f"Keyword with md5 hash '{keywordHash}' was not found in user keywords")
                return JsonResponseBadRequest(
                    additional_data={"err_id": "NO_SUCH_KEYWORD_FOUND"}
                )
            except Exception as err:
                logger.exception(err)
                return JsonResponseServerError()

            # Create a new Article instance
            article = Article(
                website=user.current_active_website,
                article_uid=generate_article_uid(user.username),
                title=custom_article_title,
                keyword=keyword_object,
            )
            article.save()

            return JsonResponse(status=200, data={"message": "OK"})
        except Exception as err:
            logger.exception(err)
            return JsonResponseBadRequest()

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def website_keyword_article_titles_api(request: Request):
    """
    Returns data for current website keyword's article titles.

    :param request: Django Rest Framework's Request object
    """
    # user: User = request.user

    if request.method == 'GET':
        try:
            keyword_hash: str = request.query_params['keywordHash']
        except KeyError:
            return JsonResponseBadRequest()

        try:
            keyword = Keyword.objects.get(keyword_md5_hash=keyword_hash)
        except Keyword.DoesNotExist:
            return JsonResponse404(additional_data={'err_id': "NO_SUCH_KEYWORD_FOUND"})

        return_data = {
            'keyword': unescape_amp_char(keyword.keyword),
            'traffic': keyword.volume,
            'difficulty': keyword.paid_difficulty,
            'articleData': KeywordDetailsTableDataSerializer(keyword.article_set.all(), many=True).data,
        }

        return JsonResponse(status=200, data=return_data, safe=False)

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def article_generation_v2_api(request: Request):
    """
    Starts article generation V2 for given article title.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            article_uid: str = request.data['article_uid']
            context: str = request.data.get('context','')
        except KeyError:
            return JsonResponseBadRequest()

         # Allow the user to generate the first article without verification
        if user.articles_generated == 0:
            # No need to check email verification for the first article
            pass

        else:
            # Check if user email is verified
            if not user.verified and not user.has_ltd_plans:
                return JsonResponseBadRequest(additional_data={'err_id': "USER_NOT_VERIFIED"})

        if context:
            article = user.articles.get(article_uid=article_uid)
            article.context = context
            article.save()

        # check user article generation limit for the month
        current_plan_data: Dict = get_stripe_product_data(user)
        article_limit: int = current_plan_data['metadata']['max_articles']

        if user.articles_generated >= article_limit:
            return JsonResponse(status=200, data={'status': "rejected", 'reason': "max_limit_reached"})

        # ------------------- Create K8 Job -------------------
        article_k8_task_response = create_article_generation_v2_task(user, article_uid)
        if article_k8_task_response['status'] == "error":
            return JsonResponseBadRequest(additional_data={'err_id': article_k8_task_response['err_id'], 'message': article_k8_task_response['message']})

        if not DEBUG:
            # Delegate provisioning task to celery
            celery_check_flyio_provisioning.delay(article_k8_task_response['machine_id'],
                                                  article_k8_task_response['job_id'],
                                                  FLY_ARTICLE_GEN_APP_NAME,
                                                  FLY_ARTICLE_GEN_DEPLOY_TOKEN)

        articles: QuerySet[Article] = user.articles.select_related(
            'keyword',
            'schedulearticleposting'
        ).only(
            'article_uid',
            'title',
            'internal_link_count',
            'external_link_count',
            'image_count',
            'word_count',
            'is_processing',
            'is_generated',
            'is_posted',
            'is_failed',
            'is_archived',
            'is_user_added',
            'article_link',
            'posted_to',
            'created_on',
            'generated_on',
            'posted_on',
            'feedback',
            'keyword__keyword',
            'keyword__keyword_md5_hash',
            'keyword__volume',
            'schedulearticleposting__schedule_on'
        ).prefetch_related(
            Prefetch('keyword__keywordproject_set',
                    queryset=KeywordProject.objects.only('project_id'))
        )

        updated_title_data = ArticleTitleTableDataSerializer(articles, many=True).data

        user.articles_generated += 1
        user.total_articles_generated += 1
        user.save()

        return JsonResponse(status=200, data={
            'status': "sent_for_processing",
            'updated_title_data': updated_title_data,
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def bulk_article_generation_v2_api(request: Request):
    """
    Starts bulk articles generation for given articles title.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    try:
        articles_uid: str = request.data['articles_uid']
    except KeyError:
        return JsonResponseBadRequest()

    # Allow the user to generate the first article without verification
    if user.articles_generated == 0 and len(articles_uid) == 1:
        # No need to check email verification for the first article
        pass

    else:
        # Check if user email is verified
        if not user.verified and not user.has_ltd_plans:
            return JsonResponseBadRequest(additional_data={'err_id': "USER_NOT_VERIFIED"})


    bulk_art_gen_jobs_res = create_bulk_article_generation_jobs(articles_uid, user)

    if bulk_art_gen_jobs_res.get('status') == 'rejected':
        if bulk_art_gen_jobs_res.get('reason') == 'max_limit_reached':
            return JsonResponse(
                status=200, data={"status": "rejected", "reason": "max_limit_reached"}
            )

        elif bulk_art_gen_jobs_res.get('reason') == 'no_articles_found':
            return JsonResponseBadRequest(
                additional_data={"err_id": "NO_SUCH_ARTICLE_FOUND"}
            )

    elif bulk_art_gen_jobs_res.get('status') == 'success':
        articles: QuerySet[Article] = user.articles.select_related(
            'keyword',
            'schedulearticleposting'
        ).only(
            'article_uid',
            'title',
            'internal_link_count',
            'external_link_count',
            'image_count',
            'word_count',
            'is_processing',
            'is_generated',
            'is_posted',
            'is_failed',
            'is_archived',
            'is_user_added',
            'article_link',
            'posted_to',
            'created_on',
            'generated_on',
            'posted_on',
            'feedback',
            'keyword__keyword',
            'keyword__keyword_md5_hash',
            'keyword__volume',
            'schedulearticleposting__schedule_on'
        ).prefetch_related(
            Prefetch('keyword__keywordproject_set',
                    queryset=KeywordProject.objects.only('project_id'))
        )

        updated_title_data = ArticleTitleTableDataSerializer(articles, many=True).data

        return JsonResponse(status=200, data={
            'status': "sent_for_processing",
            'updated_title_data': updated_title_data,
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_article_content_api(request: Request):
    """
    Retrieves article content for given article uid.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'GET':
        try:
            article_uid: str = request.query_params['article_uid']
        except KeyError:
            return JsonResponseBadRequest()

        try:
            article: Article = user.articles.get(article_uid=article_uid)
        except Article.DoesNotExist:
            return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ARTICLE"})

        try:
            suggested_internal_links = json.loads(article.suggested_internal_links)
        except (json.JSONDecodeError, TypeError):
            suggested_internal_links = []

        if article.is_generated:
            try:

                if article.is_posted:

                    if article.article_status == "publish":
                        article_link = article.article_link

                    else:

                        if not article.posted_to == "webflow":
                            extract = tldextract.extract(article.article_link)
                            if extract.subdomain:
                                article_link = f"https://{extract.subdomain}.{extract.domain}.{extract.suffix}"
                            else:
                                article_link = f"https://{extract.registered_domain}"

                        else:
                            # Redirect to webflow dashboard
                            article_link = "https://webflow.com/dashboard"

                else:
                    article_link = None

                data = {
                    'is_posted': article.is_posted,
                    'posted_to': article.posted_to,
                    'processing': False,
                    'title':  unescape_amp_char(article.title),
                    'keyword': unescape_amp_char(article.keyword.keyword) if article.keyword else None,
                    'traffic': article.keyword.volume if article.keyword else None,
                    'content': article.content,
                    'internal_links': article.internal_link_count,
                    'external_links': article.external_link_count,
                    'image_count': article.image_count,
                    'word_count': article.word_count,
                    'featured_image': article.selected_featured_image.image_url if article.selected_featured_image else None,
                    'all_integrations': user.all_integrations,
                    'all_integrations_with_unique_id': user.all_integrations_with_unique_id,
                    'external_backlinks_preference': user.external_backlinks_preference,
                    'selected_template': (article.selected_featured_image and article.selected_featured_image.template_id_used) \
                        or user.feature_image_template_id,
                    'image_source': user.image_source,
                    'article_description': article.article_description,
                    'article_feedback': article.feedback,
                    'post_link': article_link,
                    'posted_on': article.posted_on,
                    'article_post_status': article.article_status,
                    'article_language_preference': article.website.article_language_preference,
                    'suggested_internal_links': suggested_internal_links,
                    'url_slug': article.url_slug if article.url_slug else article.title.lower().replace(" ", "-"),
                    'active_integration': user.active_integration,
                }

                if user.feature_image_required:
                    data['feature_image_template'] = [
                        {
                            'template_id': "neon-style-with-text",
                            'template_name': "Neon Style with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794987/featured-image-template-sample/template-neon-text.png",
                            'tool_tip': "Glowing Horizon: Neon-infused design with bold text, perfect for modern, futuristic blogs.",
                            'label': "Premium"
                        },
                        {
                            'template_id': "neon-style-without-text",
                            'template_name': "Neon Style without Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794988/featured-image-template-sample/template-neon.png",
                            'tool_tip': "Abstract Neon Dream: Bold and vibrant neon visuals without text, ideal for tech or creative posts.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "water-color-with-text",
                            'template_name': "Water Color with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727794988/featured-image-template-sample/template-watercolor-text.png",
                            'tool_tip': "Artistic Brushstrokes: Soft watercolor textures with a vibrant touch, perfect for adding elegance with text.",
                            'label': "Premium"
                        },
                        # {
                        #     'template_id': "water-color-without-text",
                        #     'template_name': "Water Color without Text",
                        #     'sample_image_url': "https://cdn.abun.com/abun-media/dev/article-images/article-Yash-BIsht-4a11c486-4.png",
                        #     'tool_tip': "Soft & Artistic: Pastel, hand-painted style that adds a gentle, creative touch to your blog."
                        # },
                        {
                            'template_id': "retro-with-text",
                            'template_name': "Retro with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727796205/featured-image-template-sample/template-retro-text1.png",
                            'tool_tip': "Vintage Appeal: Classic, bold retro design perfect for nostalgic or themed content.",
                            'label': "Premium"
                        },
                        {
                            'template_id': "retro-without-text",
                            'template_name': "Retro without Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727796614/featured-image-template-sample/template-retro1.png",
                            'tool_tip': "Vintage Vibes: Classic retro aesthetics with bold, nostalgic color schemes.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "comic-style-with-text",
                            'template_name': "Comic Style with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790298/featured-image-template-sample/template-comic-text.png",
                            'tool_tip': "Dynamic Comic Look: Bold, energetic layout that brings a comic-book feel to your post.",
                            'label': "Premium"
                        },
                        {
                            'template_id': "comic-style-without-text",
                            'template_name': "Comic Style without Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790855/featured-image-template-sample/template-comic.png",
                            'tool_tip': "Graphic Impact: A striking, visual-only comic style that conveys energy and fun.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "doodle-sketch-with-text",
                            'template_name': "Doodle Sketch with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727789905/featured-image-template-sample/template-doodle-sketch-text.jpg",
                            'tool_tip': "Hand-drawn Charm: Casual, hand-drawn style with room for your title, adding a personal touch.",
                            'label': "Premium"
                        },
                        # {
                        #     'template_id': "doodle-sketch-without-text",
                        #     'template_name': "Doodle Sketch without Text",
                        #     'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727790154/featured-image-template-sample/template-doodle-sketch.png",
                        #     'tool_tip': "Creative Simplicity: Playful doodles without text, ideal for informal or creative posts."
                        # },
                        {
                            'template_id': "cyberpunk-with-text",
                            'template_name': "Cyberpunk with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727793530/featured-image-template-sample/template-cyberpunk-text.png",
                            'tool_tip': "Cyberpunk Sci-Fi: Futuristic, neon-lit design with space for bold titles, great for tech or futuristic themes.",
                            'label': "Premium"
                        },
                        {
                            'template_id': "cyberpunk-without-text",
                            'template_name': "Cyberpunk without Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727793530/featured-image-template-sample/template-cyberpunk.png",
                            'tool_tip': "Cyberpunk Sci-Fi: Futuristic, neon-lit design with space for bold titles, great for tech or futuristic themes.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "grunge-with-text",
                            'template_name': "Grunge with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960027/featured-image-template-sample/template-grunge-text.png",
                            'tool_tip': "Urban Edge: A gritty, textured look with distressed elements, perfect for bold and rebellious themes.",
                            'label': "Premium"
                        },
                        {
                            'template_id': "grunge-without-text",
                            'template_name': "Grunge without Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960026/featured-image-template-sample/template-grunge.jpg",
                            'tool_tip': "Urban Edge: A raw, textured design with distressed elements, capturing a bold, rebellious vibe.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "water-color-with-doodle-with-text",
                            'template_name': "Watercolor with Doodle with Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960026/featured-image-template-sample/template-watercolor-doodle-text.png",
                            'tool_tip': "Artistic Whimsy: Soft watercolor splashes paired with playful doodles, ideal for creative and light-hearted topics.",
                            'label': "Premium"
                        },
                        {
                            'template_id': "water-color-with-doodle-without-text",
                            'template_name': "Watercolor with Doodle without Text",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1727960027/featured-image-template-sample/template-watercolor-doodle.png",
                            'tool_tip': "Artistic Whimsy: Gentle watercolor strokes combined with fun doodles, creating a creative and lively visual.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "ok0l2K5mppOLZ3j1Yx",
                            'template_name': "Elegant Minimalism",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-one.png",
                            'tool_tip': "Simple Highlight: A sleek and simple design with a black background and yellow text, giving it a clean and timeless feel.",
                            'label': "Basic"
                        },
                        {
                            'template_id': "kY4Qv7D8dAaLDB0qmP",
                            'template_name': "Bright Minimalism",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522236/featured-image-template-sample/template-five.png",
                            'tool_tip': "Vibrant Simplicity: A striking, high-contrast design with large black text on a green background, putting the focus entirely on the title.",
                            'label': "Basic"
                        },
                        {
                            'template_id': "j14WwV5Vjj4R5a7XrB",
                            'template_name': "Compact Image Focus",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-two.png",
                            'tool_tip': "Balanced Layout: Small image next to bold text, perfect for articles with focused content.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "7wpnPQZzKKOm5dOgxo",
                            'template_name': "Bold Blue Contrast",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522233/featured-image-template-sample/template-three.png",
                            'tool_tip': "Eye-Catching Layout: Strong blue background to make the title pop, ideal for attention-grabbing posts.",
                            'label': "Pro"
                        },
                        {
                            'template_id': "yKBqAzZ9xwB0bvMx36",
                            'template_name': "Image Dominance",
                            'sample_image_url': "https://res.cloudinary.com/diaiivikl/image/upload/v1722522236/featured-image-template-sample/template-four.png",
                            'tool_tip': "Visual First: Large image paired with minimal text, perfect for visually-driven blog posts.",
                            'label': "Pro"
                        },
                    ]

                try:
                    schedule_article_posting = ScheduleArticlePosting.objects.get(article=article)
                except ScheduleArticlePosting.DoesNotExist:
                    schedule_article_posting = None

                if schedule_article_posting:
                    data['article_scheduled_for_posting'] = True
                    data['article_scheduled_datetime'] = schedule_article_posting.schedule_datetime.isoformat()
                else:
                    data['article_scheduled_for_posting'] = False
                    data['article_scheduled_datetime'] = None

                return JsonResponse(status=200, data=data)

            except Exception as err:
                logger.exception(err)
                return JsonResponseServerError()

        else:
            return JsonResponseBadRequest({'err_id': "ARTICLE_BAD_STATE"})

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def article_is_generated_api(request: Request):
    """
    Returns json with 'generated' as True if article has been generated. Also includes article title.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'GET':
        try:
            article_uid: str = request.query_params['article_uid']
        except KeyError:
            return JsonResponseBadRequest()

        try:
            article: Article = user.articles.get(article_uid=article_uid)
        except Article.DoesNotExist:
            return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ARTICLE"})

        # Add keyword to JsonResponse based on article type (default or how_to)
        data = {
            'generated': article.is_generated,
            'failed': article.is_failed,
            'title': unescape_amp_char(article.title),
            'keyword': unescape_amp_char(article.keyword.keyword) if article.keyword else None,
            'traffic': article.keyword.volume if article.keyword else None,
            'keyword_hash': article.keyword.keyword_md5_hash if article.keyword else None,
        }

        # get Keyword Project pf the article keyword
        if article.keyword:
            try:
                keyword_project = user.keyword_projects.filter(keywords=article.keyword).first()
                data['keyword_project_id'] = keyword_project.project_id
                data['keyword_project_name'] = keyword_project.project_name
                data['locationIsoCode'] = keyword_project.location_iso_code

            except AttributeError:
                data['keyword_project_id'] = None
                data['keyword_project_name'] = None
                data['locationIsoCode'] = None
        else:
            data['keyword_project_id'] = None
            data['keyword_project_name'] = None
            data['locationIsoCode'] = None

        return JsonResponse(status=200, data=data)

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def edit_title_api(request: Request):
    """
    Saves updated article title. Returns new title vaue on success.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == "POST":
        try:
            article_uid: str = request.data["article_uid"]
            title: str = html.escape(request.data["title"].strip())
        except KeyError:
            return JsonResponseBadRequest()

        try:
            article: Article = user.articles.get(article_uid=article_uid)
        except Article.DoesNotExist:
            return JsonResponseBadRequest(additional_data={"err_id": "NO_SUCH_ARTICLE"})

        # if article.is_posted:
        #     return JsonResponseBadRequest(additional_data={"err_id": "ARTICLE_POSTED"})

        article.title = title
        article.url_slug = title.lower().replace(" ", "-")
        article.save()

        # delete existing featured image for the article
        # if article.selected_featured_image:
        #     # remove all featured images for the article
        #     for featured_image in article.featured_images.all():
        #         article.featured_images.remove(featured_image)
        #         featured_image.delete()

        # RE-Generate and save featured image.
        # feature_image = article.selected_featured_image
        # selected_template = user.feature_image_template_id
        # if feature_image and feature_image.template_image_url:
        #     template_photo = feature_image.template_image_url
        # else:
        #     template_photo = get_unsplash_images(article.title)[0]["url"]

        # try:
        #     if not user.feature_image_required:
        #         raise Exception("Feature image generation is turned off for this website")

        #     else:
        #         if "-text" in user.feature_image_template_id:
        #             template_photo_url = "https://res.cloudinary.com/diaiivikl/image/upload/v1709385716/ai-generated-image_rv1ice.jpg"
        #             ai_image_response: Dict = generate_AI_feature_image__sync(article_uid,
        #                                                                       user.feature_image_template_label == "premium" and "segmind" or "deepinfra")
        #             save_featured_image(ai_image_response["image_url"], "ai_image_generation",
        #                                 article, user.feature_image_template_id, template_photo_url)

        #         else:
        #             # Prompt gpt to make the title shorter
        #             article_title: str = rephrase_article_title(article)

        #             own_image_response: Dict = generate_custom_feature_image(
        #                 template_photo,
        #                 article.user.current_active_website.logo_url if article.user.current_active_website else "",
        #                 article_title,
        #                 article_uid,
        #                 selected_template,
        #                 True,
        #                 article.user.images_file_format,
        #             )

        #             if own_image_response["image_url"]:
        #                 save_featured_image(
        #                     own_image_response["image_url"],
        #                     "bannerbear",
        #                     article,
        #                     selected_template,
        #                     template_photo,
        #                 )

        #             else:
        #                 raise Exception("Featured image generation failed")

        # except Exception as err:
        #     logger.error(err)
        #     default_image_url: str = "https://res.cloudinary.com/diaiivikl/image/upload/v1690186987/" \
        #                              "featured_image_1200x628.png"
        #     save_featured_image(default_image_url, 'defaultimage', article, selected_template, template_photo)

        article.save()
        return JsonResponse(status=200, data={
            'title': unescape_amp_char(article.title),
            'featured_image': article.selected_featured_image.image_url if article.selected_featured_image else None,
            'url_slug': article.url_slug
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_article_api(request: Request):
    """
    Saves updated article content.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            article_uid: str = request.data['article_uid']
            article_content: str = request.data['article_content']
            article_description: str = request.data['article_description'] if 'article_description' in request.data else None
            article_feedback: str = request.data['article_feedback'] if 'article_feedback' in request.data else None
            url_slug: str = request.data['url_slug'] if 'url_slug' in request.data else None
        except KeyError:
            return JsonResponseBadRequest()

        if article_content == '':
            return JsonResponseBadRequest()

        try:
            article: Article = user.articles.get(article_uid=article_uid)
        except Article.DoesNotExist:
            return JsonResponseRedirect("dashboard")

        article.content = article_content
        article.word_count = get_word_count(article_content)
        if article_description:
            article.article_description = article_description

        if article_feedback:
            article.feedback = article_feedback

        if url_slug:
            article.url_slug = sanitize_url_slug(url_slug.lower().replace(" ","-")[:50])
        else:
            article.url_slug = sanitize_url_slug(article.title.lower().replace(" ","-")[:50])

        article.save()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_website_settings_api(request: Request):
    """
    Saves website settings. Does not handle 'integration' value.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    # website: Website = user.current_active_website

    if request.method == "POST":
        try:
            settings_to_save: List[Dict] = request.data["settings_to_save"]

        except KeyError:
            return JsonResponseBadRequest()

        try:
            for setting in settings_to_save:
                settings_name: str = setting["settingName"]
                new_value: str = setting["settingValue"]

                if settings_name == "keyword_strategy":
                    user.keyword_strategy = new_value

                elif settings_name == "image_source":
                    user.image_source = new_value

                elif settings_name == "feature_image":
                    # allow user to use premium template if he/she has already selected a premium template
                    if user.feature_image_template_label == "premium":
                        user.feature_image_template_id = new_value

                    else:
                        # Get the user current plan
                        try:
                            current_plan_name = get_stripe_product_data(user)['display_name']
                        except stripe.error.InvalidRequestError:
                            current_plan_name = 'Trial'

                        previous_feature_image_template_id = user.feature_image_template_id
                        user.feature_image_template_id = new_value

                        # Check if user is on "Basic" or "Trial" plan and trying to use premium featured image template
                        if user.feature_image_template_label == "premium" and ("Basic" in current_plan_name or "Trial" in current_plan_name):
                            # Update it back to the previously used Featured Image Template ID
                            user.feature_image_template_id = previous_feature_image_template_id

                elif settings_name == "feature_image_required":
                    user.feature_image_required = new_value

                elif settings_name == "article_tone_of_voice":
                    user.article_tone_of_voice = new_value

                elif settings_name == "external_backlinks_preference":
                    if new_value == "off":
                        user.max_external_backlinks = 0
                    user.external_backlinks_preference = new_value

                elif settings_name == "article_language_preference":
                    user.article_language_preference = new_value

                elif settings_name == "max_internal_backlinks":
                    user.max_internal_backlinks = new_value

                elif settings_name == "max_external_backlinks":
                    user.max_external_backlinks = new_value

                elif settings_name == "max_internal_glossary_backlinks":
                    user.max_internal_glossary_backlinks = new_value

                elif settings_name == "internal_glossary_backlinks_preference":

                    user.internal_glossary_backlinks_preference = new_value

                elif settings_name == "generate_bannerbear_featured_image":
                    user.generate_bannerbear_featured_image = new_value

                elif settings_name == "ai_generated_image_style":
                    user.ai_generated_image_style = new_value

                elif settings_name == "images_file_format":
                    user.images_file_format = new_value

                elif settings_name == "article_context":
                    user.article_context = new_value

                elif settings_name == "tone_of_article":
                    user.tone_of_article = new_value

                elif settings_name == "scale_of_tone":
                    user.scale_of_tone = new_value

                elif settings_name == "show_logo_on_featured_image":
                    user.show_logo_on_featured_image = new_value
                
                elif settings_name == "active_integration":
                    user.active_integration = new_value
                
                elif settings_name == "toggle_toc":
                    user.toggle_toc = new_value
                    
                elif settings_name == "toggle_faq":
                    user.toggle_faq = new_value
                    
                elif settings_name == "toggle_tldr":
                    user.toggle_tldr = new_value

                elif settings_name == "toggle_table":
                    user.toggle_table = new_value
                
                elif settings_name == "toggle_bullet_points":
                    user.toggle_bullet_points = new_value
                
                elif settings_name == "toggle_meta_description":
                    user.toggle_meta_description = new_value
                
                elif settings_name == "auto_indexing":
                    user.auto_indexing = new_value
                    if new_value:
                        user.last_auto_indexed_on = None                        
                else:
                    # NOTE: 'integration' is handled in its own api
                    logger.error(
                        f"save_user_settings() - Bad 'settings_name' value {settings_name}"
                    )
                    return JsonResponseBadRequest()

        except KeyError:
            return JsonResponseBadRequest()

        user.save()

        return JsonResponse(status=200, data={"message": "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_email_preference_api(request: Request):
    """
    Saves website settings. Does not handle 'integration' value.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            send_notif_email: bool = request.data['send_notif_email']
        except KeyError:
            return JsonResponseBadRequest()

        user.send_notification_emails = send_notif_email
        user.save()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_website_details_api(request: Request):
    """
    Saves website details settings.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if request.method == 'POST':
        try:
            title: str = request.data['title']
            desc: str = request.data['desc']
            industry: str = request.data['ind']
            icp: str = request.data['icp']
        except KeyError:
            return JsonResponseBadRequest()

        website.title = title
        website.description = desc
        website.industry = industry
        website.icp_text = icp
        website.save()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_user_details_api(request: Request):
    """
    Saves website details settings.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            username: str = request.data['username']
            tz: str = request.data['tz']
        except KeyError:
            return JsonResponseBadRequest()

        user.username = username
        user.user_tz = tz
        user.save()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def post_article_api(request: Request):
    """
    Posts article to user's website based on integration settings. If user has not set up any integration, returns
    bad request (status code 400).

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            article_uid: str = request.data['article_uid']
            selected_integration: str = request.data['selected_integration']
            selected_integration_unique_text_id: str = request.data.get('selection_integration_id')
            post_status: Literal['draft', 'publish'] = request.data.get('post_status', 'publish')
            selected_categories = request.data.get('selected_categories')
            update_published_article = request.data.get('update_published_article', False)

        except KeyError:
            return JsonResponseBadRequest()

        try:
            article: Article = user.articles.get(article_uid=article_uid)
        except Article.DoesNotExist:
            logger.error(f"No such article exists (article uid: {article_uid})")
            return JsonResponseBadRequest()

        if selected_integration not in user.all_integrations:
            logger.error(f"'{user.email}' selected integration '{selected_integration}' is not supported.")
            return JsonResponseBadRequest()

        if "wordpress" in selected_integration and user.wordpress_integrations.exists():
            res = publish_article_to_wp(article,
                                        user,
                                        wp_site_url=selected_integration_unique_text_id,
                                        status=post_status,
                                        selected_categories=selected_categories,
                                        update_published_article=update_published_article)

            if res["status"] == "success":
                logger.debug(f"Article {article.article_uid} posted successfully!")

                if post_status == "publish":
                    article_link = res["article_link"]
                else:
                    article_link_extract = tldextract.extract(res["article_link"])

                    if article_link_extract.subdomain:
                        article_link = f"https://{article_link_extract.subdomain}.{article_link_extract.domain}.{article_link_extract.suffix}"
                    else:
                        article_link = f"https://{article_link_extract.registered_domain}"

                return JsonResponse(status=200, data={
                    'link': article_link,
                    'article_uid': article.article_uid,
                    'posted_to': article.posted_to,
                    'posted_on': article.posted_on,
                    'article_post_status': article.article_status,
                })

            else:
                logger.error(f"Article posting failed. Error message: {res['error_message']}")

                # send email notification
                email_message = unable_to_publish_article_body(user.username)
                send_email(
                    user.email,
                    ABUN_NOTIFICATION_EMAIL,
                    "Team Abun",
                    "Action Required: Fix Auto-Publishing Issue with WordPress",
                    email_message
                )

                return JsonResponse(res, status=400)

        elif "webflow" in selected_integration and user.webflow_integrations.exists():
            # Publish the article to webflow
            res = publish_article_to_wf(article,
                                        user,
                                        selected_integration_unique_text_id,
                                        status=post_status,
                                        publish_site=post_status == "publish",
                                        update_published_article=update_published_article)

            if res and res["status"] == "success":

                if res['post_status'] == "publish":
                    article_link = res["article_link"]
                else:
                    # article_link_extract = tldextract.extract(res["article_link"])

                    # if article_link_extract.subdomain:
                    #     article_link = f"https://{article_link_extract.subdomain}.{article_link_extract.domain}.{article_link_extract.suffix}"
                    # else:
                    #     article_link = f"https://{article_link_extract.registered_domain}"

                    # Redirect to webflow dashboard
                    article_link = "https://webflow.com/dashboard"

                return JsonResponse(status=200, data={
                    'link': article_link,
                    'article_uid': article_uid,
                    'posted_to': article.posted_to,
                    'posted_on': article.posted_on,
                    'article_post_status': article.article_status,
                })

            else:
                if res and res.get("error_message"):
                    logger.error(res["error_message"])
                else:
                    logger.error("Article posting failed due to unknown reason from webflow.")

                return JsonResponse(res, status=400)

        elif "wix" in selected_integration and user.wix_integrations.exists():
            # Publish the article to wix
            response: Dict = publish_article_to_wix(article,
                                                    site_id=selected_integration_unique_text_id,
                                                    status=post_status,
                                                    update_published_article=update_published_article)

            if not response["status"] == "success":
                logger.error("Article posting failed due to unknown reason from wix.")
                logger.error(response['error_message'])
                return JsonResponse(response, status=400)

            if post_status == "publish":
                article_link = response["article_link"]
            else:
                article_link_extract = tldextract.extract(res["article_link"])

                if article_link_extract.subdomain:
                    article_link = f"https://{article_link_extract.subdomain}.{article_link_extract.domain}.{article_link_extract.suffix}"
                else:
                    article_link = f"https://{article_link_extract.registered_domain}"

            return JsonResponse(status=200, data={
                'link': article_link,
                'article_uid': article_uid,
                'posted_to': article.posted_to,
                'posted_on': article.posted_on,
                'article_post_status': article.article_status,
            })

        elif "shopify" in selected_integration and user.shopify_integrations.exists():
            # Publish the article to shopify
            response: Dict = publish_article_to_shopify(article,
                                                        status=post_status,
                                                        shop_url=selected_integration_unique_text_id,
                                                        update_published_article=update_published_article)

            if not response["status"] == "success":
                logger.error("Article posting failed due to unknown reason from shopify.")
                logger.error(response['error_message'])
                error_message = response.get("error_message")
                if isinstance(error_message, bytes):
                    error_message = error_message.decode("utf-8")
                return JsonResponse({"status": "error", "error_message": error_message}, status=400)

            if post_status == "publish":
                article_link = response["article_link"]
            else:
                article_link_extract = tldextract.extract(res["article_link"])

                if article_link_extract.subdomain:
                    article_link = f"https://{article_link_extract.subdomain}.{article_link_extract.domain}.{article_link_extract.suffix}"
                else:
                    article_link = f"https://{article_link_extract.registered_domain}"

            return JsonResponse(status=200, data={
                'link': article_link,
                'article_uid': article_uid,
                'posted_to': article.posted_to,
                'posted_on': article.posted_on,
                'article_post_status': article.article_status,
            })

        elif "ghost" in selected_integration and user.ghost_integrations.exists():
            status_for_ghost = post_status == "publish" and "published" or post_status

            # Publish the article to ghost
            response: Dict = publish_article_to_ghost(article,
                                                      site_url=selected_integration_unique_text_id,
                                                      status=status_for_ghost,
                                                      update_published_article=update_published_article)

            if not response["status"] == "success":
                logger.critical(f"Article posting failed due to unknown reason from ghost cms.")
                logger.error(response['error_message'])
                return JsonResponse(response, status=400)

            if post_status == "publish":
                article_link = response["article_link"]
            else:
                article_link_extract = tldextract.extract(res["article_link"])

                if article_link_extract.subdomain:
                    article_link = f"https://{article_link_extract.subdomain}.{article_link_extract.domain}.{article_link_extract.suffix}"
                else:
                    article_link = f"https://{article_link_extract.registered_domain}"

            return JsonResponse(status=200, data={
                'link': article_link,
                'article_uid': article_uid,
                'posted_to': article.posted_to,
                'posted_on': article.posted_on,
                'article_post_status': article.article_status,
            })

        elif "ghl" in selected_integration and user.ghl_integrations.exists():
            # Publish the article to ghost
            response: Dict = publish_article_to_ghl(article,
                                                    site_id=selected_integration_unique_text_id,
                                                    status=post_status,
                                                    selected_categories=selected_categories,
                                                    update_published_article=update_published_article)

            if not response["status"] == "success":
                logger.critical(f"Article posting failed due to unknown reason from GHL.")
                return JsonResponse(response, status=400)

            if post_status == "publish":
                article_link = response["article_link"]
            else:
                article_link_extract = tldextract.extract(res["article_link"])

                if article_link_extract.subdomain:
                    article_link = f"https://{article_link_extract.subdomain}.{article_link_extract.domain}.{article_link_extract.suffix}"
                else:
                    article_link = f"https://{article_link_extract.registered_domain}"

            return JsonResponse(status=200, data={
                'link': article_link,
                'article_uid': article_uid,
                'posted_to': article.posted_to,
                'posted_on': article.posted_on,
                'article_post_status': article.article_status,
            })

        else:
            logger.critical(f"Could not post article to user's website due to bad integration")
            return JsonResponseServerError()

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def bulk_post_article_api(request: Request):
    """
    Bulk posts article to user's website based on integration settings. If user has not set up any integration, returns
    bad request (status code 400).

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    article_links: List[str] = []
    failed_articles_uid: List[str] = []

    try:
        articles_uid: List[str] = request.data['articles_uid']
        selected_integration: str = request.data['selected_integration']
        selected_integration_unique_text_id: str = request.data['selected_integration_id']
    except KeyError:
        return JsonResponseBadRequest()

    articles_list: QuerySet[Article] = user.articles.filter(article_uid__in=articles_uid,
                                                            is_generated=True)

    # Check integrations
    if not user.all_integrations:
        logger.critical(f"Could not post article to user's website {user.username} due to bad integration")
        return JsonResponseServerError()

    if not articles_list:
        return JsonResponseBadRequest()

    if "wordpress" in selected_integration and user.wordpress_integrations.exists():
        for article in articles_list:
            res = publish_article_to_wp(article, user, wp_site_url=selected_integration_unique_text_id)

            if res["status"] == "success":
                logger.debug(f"Article {article.article_uid} posted successfully!")
                article_links.append(res["article_link"])

            else:
                logger.error(f"Article posting failed. Error message: {res['error_message']}")
                failed_articles_uid.append(article.article_uid)

    elif "webflow" in selected_integration and user.webflow_integrations.exists():
        for article in articles_list:
            # Publish the article to webflow
            res = publish_article_to_wf(article, user, selected_integration_unique_text_id)

            if res and res["status"] == "success":
                article_links.append(res["article_link"])
                logger.debug(f"Article {article.article_uid} posted successfully!")
            else:
                if res and res.get("error_message"):
                    logger.error(res["error_message"])
                else:
                    logger.error("Article posting failed due to unknown reason from webflow.")

    elif "wix" in selected_integration and user.wix_integrations.exists():
        for article in articles_list:
            # Publish the article to wix
            response: Dict = publish_article_to_wix(article, site_id=selected_integration_unique_text_id)

            if not response["status"] == "success":
                logger.error("Article posting failed due to unknown reason from wix.")
                logger.error(response["error_message"])

            else:
                article_links.append(response["article_link"])
                logger.debug(f"Article {article.article_uid} posted successfully!")

    elif "shopify" in selected_integration and user.shopify_integrations.exists():
        for article in articles_list:
            # Publish the article to shopify
            response: Dict = publish_article_to_shopify(article, shop_url=selected_integration_unique_text_id)

            if not response["status"] == "success":
                logger.error("Article posting failed due to unknown reason from shopify.")
                logger.error(response["error_message"])

            else:
                article_links.append(response["article_link"])
                logger.debug(f"Article {article.article_uid} posted successfully!")

    else:
        logger.critical(f"Could not post article to user's website due to bad integration")
        return JsonResponseServerError()

    # send email notification if any article is failed to publish
    if selected_integration == "wordpress" and user.wordpress_integrations.exists() and failed_articles_uid:
        email_message: str = unable_to_publish_article_body(user.username)
        send_email(
            user.email,
            ABUN_NOTIFICATION_EMAIL,
            "Team Abun",
            "Article couldn't be published to your blog",
            email_message
        )

    if article_links:
        return JsonResponse(status=200, data={'links': article_links, 'articles_uid': articles_uid})

    else:
        return JsonResponseServerError()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def use_free_plan_api(request: Request):
    """
    Used for adding user to free tier/plan. Client side should send user to dashboard on receiving status 200.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            stripe_price_id: str = request.data['price_id']
        except KeyError:
            return JsonResponseBadRequest()

        # Get stripe customer id by registering this user on stripe if not already present.
        if not user.stripe_customer_id:
            stripe_price = stripe.Price.retrieve(stripe_price_id)
            customer_creation_ik: str = generate_stripe_event_idempotency_key(
                "customer creation", user.email, user.password
            )

            if stripe_price.currency == "inr":
                customer = stripe.Customer.create(
                    email=user.email,
                    name=user.username,
                    idempotency_key=customer_creation_ik,
                    address={
                        "city": f"'{user.email}' city",
                        "country": "india",
                        "line1": f"'{user.email}' address line1",
                        "line2": f"'{user.email}' address line2",
                        "postal_code": f"'{user.email}' postal code",
                        "state": f"'{user.email}' state"
                    }
                )
            else:
                customer = stripe.Customer.create(
                    email=user.email,
                    name=user.username,
                    idempotency_key=customer_creation_ik
                )

            stripe_customer_id = customer['id']
            user.stripe_customer_id = stripe_customer_id
            user.save()
        else:
            stripe_customer_id = user.stripe_customer_id

        free_plan_subscription_ik = generate_stripe_event_idempotency_key(
            "free plan subscription",
            user.email,
            user.username
        )

        subscription = stripe.Subscription.create(
            customer=stripe_customer_id,
            items=[
                {'price': stripe_price_id}
            ],
            idempotency_key=free_plan_subscription_ik
        )

        user.stripe_subscription_id = subscription['id']
        user.stripe_pricing_id = subscription['items']['data'][0]['price']['id']
        user.stripe_product_id = subscription['items']['data'][0]['price']['product']
        user.save()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def purchase_plan_api(request: Request):
    """
    Used for purchasing new plan. Returns stripe checkout url.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            stripe_price_id: str = request.data['price_id']
            success_url: str = request.data['success_url']
            cancel_url: str = request.data['cancel_url']
        except KeyError:
            return JsonResponseBadRequest()

        # If user already has a checkout id stored in model, use that after checking if it's valid
        if user.stripe_active_checkout_session_id:
            checkout: Dict = stripe.checkout.Session.retrieve(user.stripe_active_checkout_session_id)
            if (checkout['status'] == 'open') and (
                    checkout['invoice'] is not None):  # other statuses are 'expired' and 'complete'
                checkout_price_id: Dict = stripe.Invoice.retrieve(checkout['invoice'])['lines']['data'][0]['price'][
                    'id']
                # If checkout attempt is for same price, use the same checkout session.
                if checkout_price_id == stripe_price_id:
                    checkout_url: str = checkout['url']
                # Otherwise expire existing session and create a new one.
                else:
                    stripe.checkout.Session.expire(user.stripe_active_checkout_session_id)
                    checkout_url: str = create_checkout_session(user, stripe_price_id, success_url, cancel_url)
            # If checkout session stored in user model is not open or invoice is not available,
            # create a new checkout session.
            else:
                checkout_url: str = create_checkout_session(user, stripe_price_id, success_url, cancel_url)
        # If user does not have any existing checkout id stored in model, create and start a new checkout session.
        else:
            checkout_url: str = create_checkout_session(user, stripe_price_id, success_url, cancel_url)

        return JsonResponse(status=200, data={
            'checkout_url': checkout_url
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def change_plan_api(request: Request):
    """
    Used for upgrading/downgrading plans. For upgrade, we are charging immediately and switching them to new plan. For
    downgrades, we only switch plan at the end of current billing cycle.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            stripe_price_id: str = request.data['price_id']
        except KeyError:
            return JsonResponseBadRequest()

        current_product: Dict = get_stripe_product_data(user)
        price = stripe.Price.retrieve(stripe_price_id)
        new_product: Dict = get_stripe_product_data_by_id(price['product'])
        # trial_product: Dict = get_stripe_product_data_by_name('trial')

        # Don't proceed if new price is for free plan. Use plan cancellation for moving to free plan instead.
        # if (trial_product['inr']['id'] == stripe_price_id) or (trial_product['usd']['id'] == stripe_price_id):
        #     return JsonResponseBadRequest()

        # Set the default payment option (if not already set)
        stripe_customer = stripe.Customer.retrieve(user.stripe_customer_id)

        if not stripe_customer['default_source']:
            # Fetch the user's payment methods
            payment_methods = stripe.PaymentMethod.list(
                customer=user.stripe_customer_id,
                type="card"
            )

            if not payment_methods['data']:
                logger.error("No payment method is added.")
                return JsonResponseBadRequest()

            # Set the first payment method as the default
            payment_method_id = payment_methods['data'][0]['id']
            stripe.Customer.modify(
                user.stripe_customer_id,
                invoice_settings={
                    'default_payment_method': payment_method_id
                }
            )

            logger.info(f"{user.email} default payment method set.")

        # if user has any open unpaid invoice
        # if latest_invoice_is_open(user.stripe_customer_id):
        #     logger.debug("Cancelling subscription and marking invoice as uncollectible...")
        #     # cancel current subscription and close its invoice first before changing to new plan.
        #     latest_invoice = stripe.Invoice.list(customer=user.stripe_customer_id)['data'][0]
        #     stripe.Invoice.void_invoice(latest_invoice['id'])
        #     stripe.Subscription.delete(latest_invoice['subscription'])

        # Fetch existing (successful) subscription details and check if new plan is higher or lower tier.
        # Perform upgrade or downgrade based on that.
        sub = stripe.Subscription.retrieve(user.stripe_subscription_id)
        sub_item_id: str = sub['items']['data'][0]['id']

        # Check if the subscription has 'send_invoice' collection method
        if sub.get('collection_method') == 'send_invoice':
            # First update the collection method separately
            stripe.Subscription.modify(
                user.stripe_subscription_id,
                collection_method='charge_automatically'
            )

        # We are using payment_behavior='pending_if_incomplete' here because that allows us to prevent stripe from
        # moving users to new plan if their playment failed.
        # Check out https://stripe.com/docs/billing/subscriptions/pending-updates
        # Note that this also moves subscription to incomplete status because of 3D Secure. But then it becomes
        # 'succeeded' once 3D Secure verification is done successfully.
        if new_product['usd']['amount'] > current_product['usd']['amount']:
            response = stripe.Subscription.modify(
                user.stripe_subscription_id,
                items=[{'id': sub_item_id, 'price': stripe_price_id}],
                payment_behavior='pending_if_incomplete',
                proration_behavior='none',
                billing_cycle_anchor='now'
            )
        else:
            response = stripe.Subscription.modify(
                user.stripe_subscription_id,
                items=[{'id': sub_item_id, 'price': stripe_price_id}],
                payment_behavior='pending_if_incomplete',
                proration_behavior='none',
            )

        invoice_id: str = response['latest_invoice']
        invoice = stripe.Invoice.retrieve(invoice_id)

        if invoice['status'] == 'open':
            payment_intent_id: str = invoice['payment_intent']
            payment_intent = stripe.PaymentIntent.retrieve(payment_intent_id)

            if payment_intent['status'] == 'requires_payment_method':
                logger.debug(f"PaymentIntent {payment_intent_id} is in 'requires_payment_method' state")
                client_secret: str = payment_intent['client_secret']
                if payment_intent['last_payment_error']:
                    # For card declines
                    if payment_intent['last_payment_error']['code'] == 'card_declined':
                        return JsonResponse(status=200, data={'success': False,
                                                              'pi_id': payment_intent_id,
                                                              'client_secret': client_secret,
                                                              'pi_status': 'card_declined'})

                return JsonResponse(status=200, data={'success': False,
                                                      'pi_id': payment_intent_id,
                                                      'client_secret': client_secret,
                                                      'pi_status': 'requires_payment_method'})

            elif payment_intent['status'] == 'requires_action':
                # example -> 3DSecure Auth
                logger.debug(f"PaymentIntent {payment_intent_id} is in 'requires_action' state")
                client_secret: str = payment_intent['client_secret']
                return JsonResponse(status=200, data={'success': False,
                                                      'pi_id': payment_intent_id,
                                                      'client_secret': client_secret,
                                                      'pi_status': 'requires_action'})

            else:
                logger.debug(
                    f"PaymentIntent {payment_intent_id} is in and unhandled state '{payment_intent['status']}'")
                client_secret: str = payment_intent['client_secret']
                return JsonResponse(status=200, data={'success': False,
                                                      'pi_id': payment_intent_id,
                                                      'client_secret': client_secret,
                                                      'pi_status': 'unknown'})

        else:
            # user payment was successful
            return JsonResponse(status=200, data={'success': True, 'new_price_id': stripe_price_id})

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def checkout_success_api(request: Request):
    """
    Used for purchasing new plan. Returns stripe checkout url.

    :param request: Django Rest Framework's Request object.
    """
    if request.method == 'GET':
        try:
            checkout_session_id: str = request.query_params['checkout_session_id']
        except KeyError as k:
            logger.error(f"missing key {k}")
            return JsonResponseBadRequest()

        checkout_data = stripe.checkout.Session.retrieve(checkout_session_id)
        subscription_data = stripe.Subscription.retrieve(checkout_data['subscription'])
        invoice = stripe.Invoice.retrieve(checkout_data['invoice'])
        product_data = stripe.Product.retrieve(subscription_data['items']['data'][0]['price']['product'])

        plan_name: str = product_data['name']
        stripe_customer_id: str = subscription_data['customer']
        charge: str = invoice['charge']
        currency: str = invoice["currency"]
        total: int = invoice["total"] // 100

        return JsonResponse(status=200, data={
            'plan_name': plan_name,
            'customer_id': stripe_customer_id,
            'charge_id': charge,
            'currency': currency.lower(),
            'amount': total
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def card_payment_failed_api(request: Request):
    """
    Used for voiding invoice generated by subscription pending update request.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            pi_id: str = request.data['payment_intent_id']
        except KeyError:
            return JsonResponseBadRequest()

        logger.debug(pi_id)
        pi: Dict = stripe.PaymentIntent.retrieve(pi_id)
        stripe.Invoice.void_invoice(pi['invoice'])

        logger.debug(f"voided invoice {pi['invoice']} for user {user.email}")

        return JsonResponse(status=200, data={'message': f"OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_plan_data_api(request: Request):
    """
    For fetching all active Stripe product data.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'GET':
        plans: List[Dict] = get_all_product_data(user)
        return JsonResponse(status=200, data=plans, safe=False)

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_subscription_history_api(request: Request):
    """
    For fetching all of user's subscriptions till date.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'GET':
        subscription_history = []

        if user.stripe_customer_id:
            subscriptions = stripe.Subscription.list(customer=user.stripe_customer_id, status='all')

            for sub in subscriptions['data']:
                product_id: str = sub['items']['data'][0]['price']['product']
                product_data: Dict = get_stripe_product_data_by_id(product_id)

                # Retrieve the latest invoice for the subscription
                invoices = stripe.Invoice.list(subscription=sub['id'], limit=1)
                latest_invoice = invoices.data[0] if invoices.data else None

                if latest_invoice:
                    amount_paid = latest_invoice.amount_paid
                else:
                    amount_paid = sub['items']['data'][0]['price']['unit_amount']

                subscription_history.append({
                    'active': sub['canceled_at'] is None,
                    'subscription_id': sub['id'],
                    'price_id': sub['items']['data'][0]['price']['id'],
                    'plan_name': product_data['name'],
                    'currency': sub['items']['data'][0]['price']['currency'],
                    'amount': amount_paid,
                    'created': datetime.datetime.fromtimestamp(
                        sub['created']
                    ).strftime("%d %b %Y"),
                    'created_date': datetime.datetime.fromtimestamp(
                        sub['created']
                    ).date(),
                    'current_period_start': datetime.datetime.fromtimestamp(
                        sub['current_period_start']
                    ).strftime("%d %b %Y"),
                    'current_period_end': datetime.datetime.fromtimestamp(
                        sub['current_period_end']
                    ).strftime("%d %b %Y"),
                })

        if user.appsumo_licenses.exists():
            for appsumo_license in user.appsumo_licenses.all():
                subscription_history.append({
                    'active': appsumo_license.license_status == "active",
                    'subscription_id': f"appsumo-ltd-id-{appsumo_license.id}",
                    'price_id': f"appsumo-ltd-price-id-{appsumo_license.id}",
                    'plan_name': f"Appsumo Tier {appsumo_license.tier}",
                    'currency': "usd",
                    # Stripe plans are priced in multiples of 100, so we need to multiply the price of Appsumo LTD plan by 100 as well.
                    'amount': appsumo_license.plan_amount_int * 100,
                    'created': appsumo_license.created_on.strftime("%d %b %Y"),
                    'created_date': appsumo_license.created_on.date(),
                    'current_period_start': None,
                    'current_period_end': None,
                })

        # Sort the subscription based on created date
        subscription_history = sorted(subscription_history,
                                      key=lambda subscriptoin: subscriptoin['created_date'],
                                      reverse=True)

        return JsonResponse(status=200, data=subscription_history, safe=False)

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_stripe_portal_link_api(request: Request):
    """
    Generates stripe customer portal link.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'GET':
        if user.stripe_customer_id:
            portal = stripe.billing_portal.Session.create(
                customer=user.stripe_customer_id,
                return_url=STRIPE_CUSTOMER_PORTAL_RETURN_URL,
            )

            return JsonResponse(status=200, data={'url': portal['url']})

        else:
            return JsonResponse(status=200, data={'url': None})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def resend_verification_email_api(request: Request):
    """
    (Re)Sends verification email to user email id.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        send_verification_email(user)
        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


# @api_view(['GET'])
# @permission_classes([IsAuthenticated])
# def get_content_plan_data_api(request: Request):
#     """
#     returns all content plan page data.

#     :param request: Django Rest Framework's Request object.
#     """
#     user: User = request.user
#     website = user.current_active_website

#     if request.method == 'GET':
#         """
#         We need to fetch the following values:
#         1. Total Keywords
#         2. Total Achievable Traffic
#         """

#         # ------------- Total Keywords & Total Achievable Traffic -------------
#         """
#         we will be showing a range of traffic. (small value - big value)
#         small value = Calculating all the traffic from all keywords
#         big value = Calculating all the traffic from all keywords.
#         If any keyword has 0 traffic, we use it as 50 for calculation
#         """
#         total_comp_keywords: int = website.competitors.select_related('keywords').aggregate(
#             total_comp_kw=Count('keywords')
#         )['total_comp_kw']
#         # total_keywords: int = website.keywords.count() + total_comp_keywords

#         # TOTAL_COMPETITORS_TO_SHOW = 10
#         TOTAL_ARTICLES_TO_SHOW = 10
#         TOTAL_ARTICLES = (website.keywords.count() + total_comp_keywords) * 3

#         domain_data = {
#             'organic_traffic': website.organic_traffic,
#             'organic_keywords': website.organic_keywords,
#             'domain_authority': website.domain_authority,
#             'total_backlinks': website.total_backlinks,
#             'follow_count': website.follow_count,
#             'no_follow_count': website.no_follow_count,
#             'referring_domains_count': website.referring_domains_count,
#         }

#         return JsonResponse(status=200, data={
#             'website_name': website.name,
#             'website_domain': website.domain,
#             'website_logo_url': website.logo_url,
#             'website_title': website.title,
#             'website_description': website.description,
#             'website_icp': website.icp_text,
#             'website_industry': website.industry,
#             'competitor_domains': list(website.competitor_set.all().values_list('domain', flat=True)),
#             'article_titles': list(
#                 website.article_set.values_list('title', flat=True)[:TOTAL_ARTICLES_TO_SHOW]
#             ),
#             'additional_articles_count': TOTAL_ARTICLES - TOTAL_ARTICLES_TO_SHOW,
#             # 'total_articles': TOTAL_ARTICLES,
#             # 'total_keywords': total_keywords,
#             'domain_data': domain_data,
#             'performance_chart_url': website.content_plan_performance_chart_url,
#         })

#     else:
#         return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def wordpress_integration_auth(request: Request):
    """
    Checks and returns authorization endpoint for wordpress integration.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    try:
        current_plan = get_stripe_product_data(user)
        current_plan_name = current_plan['name']

    except stripe.error.InvalidRequestError:
        current_plan = None
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    wordpress_website_limit = current_plan['metadata']['websites']

    if user.wordpress_integrations.count() >= wordpress_website_limit:
        return JsonResponseBadRequest(additional_data={
            'err_id': "WEBSITE_LIMIT_EXCEEDS",
            'message': f"Your Wordpress sites limit has been reached. Please upgrade your plan to increase the limit."
        })

    try:
        wp_site_url: str = request.data['wp_site_url']
        wp_username: str = request.data.get('wp_username')
        wp_password: str = request.data.get('wp_password')
    except KeyError as key:
        logger.error(f"wordpress_integration_auth() error - Missing key {key}")
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_SITE_URL"})

    wordpress_integration = user.wordpress_integrations.filter(site_url=wp_site_url)

    if wordpress_integration.exists():
        return JsonResponse(status=200, data={'success': False, 'message': "This site is already connected to your Abun account."})

    try:
        # Fetch the wordpress routes
        routes = get_wordpress_routes(wp_site_url)
    except requests.exceptions.SSLError:
        return JsonResponse(status=200, data={
            'success': False,
            'message': "⚠️Connection failed! Your site isn’t on HTTPS and missing a valid SSL. Enable SSL and retry."
        })

    if not routes:
        return JsonResponse(status=200, data={
            'success': False,
            'err_id': "FAILED_TO_FETCH_REST_ROUTES",
            'message': "Failed to fetched the rest routes. Please contact us for further support."
        })

    if wp_username and wp_password:
        # Authenticate wordpress username and password
        api_url = routes['user_and_password_authorization']

        try:

            HEADERS = {
                "User-Agent": random.choice(USER_AGENTS)
            }

            response = requests.get(api_url,
                                    auth=HTTPBasicAuth(wp_username, wp_password),
                                    headers=HEADERS)

            if response.status_code != 200:
                return JsonResponse(status=200, data={'success': False, 'message': "Invalid username or password"})

        except Exception as err:
            logger.critical(f"wordpress_integration_auth() error - {err}")
            return JsonResponseServerError()

        # Store the auth data in the database
        add_wordpress_integration(user, routes['site_url'], wp_username, wp_password)

        return JsonResponse(status=200, data={'success': True})


    # For more info check -> https://make.wordpress.org/core/2020/11/05/application-passwords-integration-guide/
    try:
        f = Fernet(WP_DOMAIN_ENCRYPTION_KEY)
        encrypted_domain: bytes = f.encrypt(user.current_active_website.domain.encode('utf-8') if user.current_active_website is not None else "abun".encode('utf-8'))
        success_url = f"{WP_RETURN_URL_DOMAIN}/integration/wp/success/{encrypted_domain.decode('utf-8')}"
        reject_url = f"{WP_RETURN_URL_DOMAIN}/integrations"
        app_name = "Abun-" + str(uuid.uuid4()).split('-')[0]

        authorization_endpoint = routes['redirect_authorization']
        authorization_endpoint += f"?app_name={app_name}" \
                                    f"&success_url={success_url}" \
                                    f"&reject_url={reject_url}"

        return JsonResponse(
                status=200,
                data={
                    'success': True,
                    'authorization_endpoint': authorization_endpoint
                }
            )

    except Exception as err:
        logger.error(f"wordpress_integration_auth() error - {err}")
        return JsonResponse(status=200, data={'success': False})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def wordpress_integration_category_auth(request: Request):
    """
    Retrieves category details from the WordPress site without authentication.

    :param request: Django Rest Framework's Request object.
    """
    try:
        # Get WordPress site URL from request
        wp_site_url = request.data.get('wp_site_url')
    except KeyError:
        return JsonResponse({'success': False, 'message': 'Missing required parameters: wp_site_url'}, status=400)

    # Fetch the wordpress routes
    routes = get_wordpress_routes(wp_site_url)

    if not routes:
        return JsonResponse({'success': False, 'message': 'Failed to fetch wordpress rest routes.'})

    # Construct the categories API URL
    api_url = routes['categories']

    try:
        HEADERS = {
            "User-Agent": random.choice(USER_AGENTS)
        }

        # Make the request to the WordPress categories endpoint
        response = requests.get(api_url, headers=HEADERS)

        # Check if the category retrieval was successful
        if response.status_code == 200:
            categories = response.json()
             # Extract only the desired fields (name, description, slug, and count)
            filtered_categories = [
                {
                    'id':   category['id'],
                    'name': category['name'],
                }
                for category in categories
            ]
            # Sorting the categories by 'id' in ascending order
            categories_sorted = sorted(filtered_categories, key=lambda x: x['id'])
            return JsonResponse({'success': True, 'categories': categories_sorted}, status=200)
        else:
            return JsonResponse({'success': False, 'message': 'Failed to fetch categories'}, status=response.status_code)
    except Exception as err:
        logger.error(f"Error fetching categories: {err}")
        return JsonResponse({'success': False, 'message': 'Error connecting to WordPress site'}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def wordpress_add_category(request: Request):
    """
    Adds a new category to the WordPress site with specified details after WordPress authentication.
    :param request: Django Rest Framework's Request object.
    """
    # Extract necessary data from the request
    wp_site_url = request.data.get('wp_site_url')
    category_name = request.data.get('name')
    description = request.data.get('description', '')

    # Validate inputs
    if not all([wp_site_url, category_name]):
        return Response({"success": False, "message": "Missing required fields."}, status=400)

    user = request.user  # Get the authenticated user

    # Call the wordpress_integration_auth API internally to fetch the credentials
    try:
        integration = user.wordpress_integrations.filter(site_url=wp_site_url).first()
        if not integration:
            return Response({"success": False, "message": "WordPress site integration not found."}, status=404)

        # Use the stored credentials from the integration
        wp_username = integration.user_login
        wp_password = integration.password

    except Exception as e:
        logger.error(f"Error fetching WordPress credentials: {e}")
        return Response({"success": False, "message": "Failed to fetch WordPress credentials."}, status=500)

    # Fetch the wordpress routes
    routes = get_wordpress_routes(wp_site_url)

    if not routes:
        return JsonResponse({'success': False, 'message': 'Failed to fetch the wordpress rest routes.'})

    # Authenticate with the WordPress site
    auth_url = routes['user_and_password_authorization']

    HEADERS = {
        "User-Agent": random.choice(USER_AGENTS)
    }

    try:
        auth_response = requests.get(auth_url,
                                     auth=HTTPBasicAuth(wp_username, wp_password),
                                     headers=HEADERS)

        if auth_response.status_code != 200:
            return Response({"success": False, "message": "Invalid WordPress username or password"}, status=401)

    except Exception as e:
        logger.error(f"Authentication error with WordPress site: {e}")
        return Response({"success": False, "message": "Failed to connect to WordPress site."}, status=500)

    # Add new category if authentication is successful
    category_url = routes['categories']
    category_payload = {
        'name': category_name,
        'description': description,
    }
    try:
        category_response = requests.post(category_url,
                                          json=category_payload,
                                          auth=HTTPBasicAuth(wp_username, wp_password),
                                          headers=HEADERS)

        if category_response.status_code == 201:
            return Response({"success": True, "category": category_response.json()}, status=201)
        else:
            return Response({"success": False, "message": "Failed to add category"}, status=category_response.status_code)

    except Exception as e:
        logger.error(f"Error adding category to WordPress site: {e}")
        return Response({"success": False, "message": "Error adding category."}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def wordpress_post_category_checked(request: Request):
    """
    Add a new category for a given article title.
    """
    try:
        article_title = request.data.get('article_title')
        category_id = request.data.get('category_id')
        selected_integration = request.data.get('selectedIntegration')

        if not article_title or not category_id:
            return JsonResponse({'success': False, 'message': 'Missing required parameters: article_title or category_id'}, status=400)

        # Try to find an existing entry for the given article_title
        if "wordpress" in selected_integration:
            existing_category = WordpressCategories.objects.filter(article_title=article_title).first()
            integrationSerializer = WordpressCategoriesSerializer

        else:
            existing_category = GHLCategories.objects.filter(article_title=article_title).first()
            integrationSerializer = GhlSaveCategoriesSerializer

        if existing_category:
            # If the article title exists, update the category_id
            existing_category.category_id = category_id
            existing_category.save()

            data = integrationSerializer(existing_category).data
            return JsonResponse({'success': True, 'message': 'Category updated successfully', 'data': data}, status=200)

        data = {
            'article_title': article_title,
            'category_id': category_id,
        }
        serializer = integrationSerializer(data=data)

        if serializer.is_valid():
            serializer.save()
            return JsonResponse({'success': True, 'message': 'Category saved successfully', 'data': serializer.data}, status=201)
        else:
            return JsonResponse({'success': False, 'message': 'Validation failed', 'errors': serializer.errors}, status=400)

    except Exception as e:
        logger.critical(e)
        return JsonResponse({'success': False, 'message': str(e)}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def wordpress_get_category_checked(request: Request):
    """
    Retrieve the category for a given article title.
    """
    try:
        article_title = request.query_params.get('article_title','')

        # Try to find the existing entry for the given article_title
        existing_category = WordpressCategories.objects.filter(article_title=article_title).first()

        if existing_category:
            # If an entry is found, return the category details
            return JsonResponse({'success': True, 'message': 'Category found', 'category_id': existing_category.category_id}, status=200)

        return JsonResponse({'success': True, 'message': 'Category Default', 'category_id': 1}, status=200)

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def webflow_integration_auth(request: Request):
    """
    Checks and returns authorization endpoint for webflow integration.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    based_on: str = request.data['based_on']

    try:
        current_plan = get_stripe_product_data(user)
        current_plan_name = current_plan['name']
    except stripe.error.InvalidRequestError:
        current_plan = None
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponse(status=200, data={
            "success": False,
            "message": "Please upgrade your plan to enable the Webflow integration."
        })

    webflow_website_limit = current_plan['metadata']['websites']

    if user.webflow_integrations.count() >= webflow_website_limit:
        return JsonResponse(status=200, data={
            "success": False,
            "message": "Your Webflow sites limit has been reached. Please upgrade your plan to increase the limit."
        })

    if based_on == 'api':
        try:
            api_token: str = request.data['token']
            command: str = request.data['execute']
        except KeyError:
            return JsonResponseKeyError()

        if command == 'authenticate':
            res = get_webflow_site_id_and_url(api_token)

            if not res['status'] == 200:
                logger.error(res)
                res['status'] = 200
                res['data'] = {
                    'message': "We were unable to authenticate your Webflow API token. " \
                                "Please make sure all necessary permissions are allowed and API token is valid."
                }
                return JsonResponse(**res)

            site_id = res['data']['site_id']
            site_url = res['data']['site_url']

            collections = fetch_webflow_cms_collections(api_token, site_id)

            collections_details = []

            if collections is None:
                return JsonResponse(status=200, data={
                    'success': False,
                    'message': "You are making too many requests! Please try again after sometime."
                })

            for collection in collections:
                collections_details.append({
                    'collection_id': collection['id'],
                    'display_name': collection['displayName']
                })

            return JsonResponse(status=200, data={'message': "OK", 'success': True, 'collections': collections_details})

        elif command == 'fields':
            try:
                collection_id = request.data['collection_id']
            except KeyError:
                return JsonResponseKeyError()

            if user.webflow_integrations.filter(collection_id=collection_id).exists():
                return JsonResponse(status=200, data={
                    'success': False,
                    'message': f"This CMS collection is already connected."
                })

            fields = fetch_webflow_cms_collection_fields(api_token, collection_id)

            if fields is None:
                return JsonResponse(status=200, data={
                    'success': False,
                    'message': "You are making too many requests! Please try again after sometime."
                })

            supported_feilds_type = ['RichText', 'Image', 'DateTime', 'PlainText']
            all_fields = []
            has_rich_text_field = False
            has_image_field = False
            total_required_fields = 0

            for field in fields:
                field['displayName'] = ' '.join(map(lambda name: name.capitalize(), field['displayName'].split(' ')))

                if field['isRequired']:
                    if field['type'] not in supported_feilds_type:
                        return JsonResponse(status=200, data={'success': False, 'message': f"We do not support the required '{field['type']}' field. "
                                                                                           f"Please choose another CMS collection or mark the field "
                                                                                           f"as optional using the Webflow site designer."})
                    total_required_fields += 1

                if field['type'] == 'RichText':
                    has_rich_text_field = True
                elif field['type'] == 'Image':
                    has_image_field = True

                if field['type'] == 'PlainText' and field['displayName'] == 'Name':
                    field['displayName'] = 'Title'

                if field['type'] == 'Image' and field['displayName'] == 'Image':
                    field['displayName'] = 'Featured Image'

                all_fields.append({
                    'field_id': field['id'],
                    'display_name': field['displayName'],
                    'slug': field['slug'],
                    'required': field['isRequired'],
                    'field_type': field['type'],
                    'validations': field['validations']
                })

            if not has_rich_text_field:
                return JsonResponse(status=200, data={'success': False, 'message': "You don't have a RichText field for article content in this CMS "
                                                                                   "collection. Please select another CMS collection."})

            if not has_image_field:
                return JsonResponse(status=200, data={'success': False, 'message': "You don't have a Image field for article feature image in this CMS "
                                                                                   "collection. Please select another CMS collection."})

            return JsonResponse(status=200, data={
                'message': "OK",
                'success': True,
                'fields': all_fields,
                'required_feilds': total_required_fields
            })

        elif command == 'connect':
            try:
                collection_id: str = request.data['collection_id']
                fields_mapping: List[Dict] = request.data['fields_mapping']
            except KeyError:
                return JsonResponseKeyError()

            # get site id
            res = get_webflow_site_id_and_url(api_token)

            if not res['status'] == 200:
                return JsonResponse(**res)

            site_id = res['data']['site_id']
            site_url = res['data']['site_url']
            collection_slug = get_webflow_cms_collection_slug(api_token, collection_id)

            add_webflow_integration(
                user,
                api_token,
                site_id,
                fields_mapping,
                collection_id,
                collection_slug,
                site_url,
                based_on='api'
            )

            return JsonResponse(status=200, data={'message': "OK", 'success': True})

        else:
            return JsonResponseBadRequest(additional_data={'message': f"'{command}' is mot a valid command to execute."})

    # elif based_on == 'app':
    #     oauth_url = 'https://webflow.com/oauth/authorize'
    #     state = secrets.token_hex(16)

    #     # Add the state to redis task data db
    #     with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
    #         redis_connection.set(f'webflow_oauth_state_{user.email}', state)
    #         redis_connection.expire(f'webflow_oauth_state_{user.email}', 600)  # 10 minutes

    #     params = {
    #         'client_id': WEBFLOW_CLIENT_ID,
    #         'response_type': "code",
    #         'scope': WEBFLOW_SCOPE,
    #         # Using wordpress return url domain
    #         'redirect_uri': f"{WP_RETURN_URL_DOMAIN}/integration/webflow/success",
    #         'state': state
    #     }
    #     authorization_endpoint = f"{oauth_url}?" + urlencode(params)

    #     return JsonResponse(status=200, data={'success': True, 'authorization_endpoint': authorization_endpoint})

    else:
        return JsonResponseBadRequest(additional_data={'success': False, 'message': f"'{based_on}'"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def google_integrations_auth(request: Request):
    """
    Checks and returns authorization endpoint for wordpress integration.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={"err_id": "NO_WEBSITE_CONNECTED"})

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    try:
        integration_type: str = request.data['integration_type']
    except KeyError:
        logger.error("google_integration_auth() error - Missing key 'integration_type'")
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

    # Define the scope & attribute name
    if integration_type == 'google-search-console':
        scopes = ['https://www.googleapis.com/auth/webmasters.readonly',
                  'https://www.googleapis.com/auth/indexing',]
        attribute_name = 'google_search_console_integrated'
        file_name = 'client_secret_gsc.json'

    else:
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_INTEGRATION_TYPE"})

    # Check if the selected google app is already integrated
    integrated = getattr(website, attribute_name)

    if integrated:
        # Remove the integration
        integration = GoogleIntegration.objects.get(website=website, integration_type=integration_type)
        integration.delete()

    try:
        # Using wordpress return url domain
        redirect_url = f"{WP_RETURN_URL_DOMAIN}/integration/google/success"

        # Create authorization flow
        flow = Flow.from_client_secrets_file(
            os.path.join(os.path.dirname(os.path.abspath(__file__)), f'../google_client_secret/{file_name}'),
            scopes=scopes,
            redirect_uri=redirect_url
        )

        # Get the authorization url
        authorization_url, state = flow.authorization_url(prompt="consent")

        # Add the state to redis task data db
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(f'oauth_state_{attribute_name}_{website.domain}', state)
            redis_connection.expire(f'oauth_state_{attribute_name}_{website.domain}', 600)  # 10 minutes

        return JsonResponse(
            status=200,
            data={
                'success': True,
                'authorization_endpoint': authorization_url
            }
        )

    except Exception as err:
        logger.critical(err)
        return JsonResponse(
            status=500,
            data={'success': False, 'authorization_endpoint': ""}
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def remove_all_integrations_api(request: Request):
    """
    Removes current integration from website.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            integration = request.data['integration_type']
            integration_unique_text_id = request.data.get('integration_unique_id')
        except KeyError:
            return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

        if integration in ['wordpress', 'webflow', 'wix', 'shopify', 'ghost', 'ghl']:
            reset_website_integrations(user, integration, integration_unique_text_id=integration_unique_text_id)
        elif integration in ['google-search-console']:
            reset_google_integration(user, integrated_app=integration)
        else:
            return JsonResponseBadRequest(additional_data={'err_id': "INVALID_INTEGRATION"})

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_website_industry_api(request: Request):
    """
    Sets website industry field to new value.
    """
    user: User = request.user
    website: Website = user.current_active_website

    if request.method == 'POST':
        try:
            industry: str = html.escape(request.data['industry']).strip().replace("&amp;", "&")
        except KeyError:
            return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

        if website.industry:
            website.industry = industry
            website.save()
        else:
            return JsonResponseBadRequest(additional_data={'err_id': "BAD_INDUSTRY_VALUE"})

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_website_icp_api(request: Request):
    """
    Sets website ICP field to new value.
    """
    user: User = request.user
    website: Website = user.current_active_website

    if request.method == 'POST':
        try:
            icp: str = html.escape(request.data['icp']).strip().replace("&amp;", "&")
        except KeyError:
            return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

        if website.industry:
            website.icp_text = icp
            website.save()
        else:
            return JsonResponseBadRequest(additional_data={'err_id': "BAD_ICP_VALUE"})

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def toggle_auto_scan_website_api(request: Request):
    """
    API to enable/disable auto scanning for the user's current active website.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_ACTIVE_WEBSITE", 'message': "No active website found."})

    if request.method == 'POST':
        try:
            auto_scan_enabled: bool = request.data['auto_scan_enabled']
        except KeyError:
            return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS", 'message': "Missing auto_scan_enabled parameter."})

        # Update the auto_scan_website field
        website.auto_scan_website = auto_scan_enabled
        website.save()

        logger.info(f"Auto scan {'enabled' if auto_scan_enabled else 'disabled'} for website: {website.domain}")

        return JsonResponse(status=200, data={
            'message': "OK",
            'auto_scan_enabled': auto_scan_enabled
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def toggle_auto_schema_api(request: Request):
    """
    API to enable/disable AI auto schema for the user's current active website.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_ACTIVE_WEBSITE", 'message': "No active website found."})

    if request.method == 'POST':
        try:
            auto_schema_enabled: bool = request.data['auto_schema_enabled']
        except KeyError:
            return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS", 'message': "Missing auto_schema_enabled parameter."})

        # Update the auto_schema_enabled field
        website.auto_schema_enabled = auto_schema_enabled
        website.webpage_set.update(schema_enabled=auto_schema_enabled)
        website.save()

        logger.info(f"AI auto schema {'enabled' if auto_schema_enabled else 'disabled'} for website: {website.domain}")

        return JsonResponse(status=200, data={
            'message': "OK",
            'auto_schema_enabled': auto_schema_enabled
        })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def download_article(request: Request):
    """
    API for downloading article in given format.
    """
    user: User = request.user

    try:
        article_uid: str = request.query_params['article_uid']
        download_type: str = request.query_params['download_type']
    except KeyError:
        return Response(status=400)

    try:
        article: Article = user.articles.get(article_uid=article_uid)
    except Article.DoesNotExist:
        return Response(status=404)

    if download_type == 'md':
        file = io.BytesIO(article.content.encode('utf-8'))
        response = Response(
            content_type='text/markdown'
        )
        response.content = file
        return response

    elif download_type == 'html':
        file = io.BytesIO(markdown.markdown(article.content).encode('utf-8'))
        response = Response(
            content_type='text/html'
        )
        response.content = file
        return response

    elif download_type == 'docx':
        document = Document()
        new_parser = HtmlToDocx()
        new_parser.add_html_to_document(markdown.markdown(article.content), document)
        response = Response(
            content_type="application/vnd.openxmlformats-officedocument.wordprocessingml.document"
        )
        response.content = document
        return response

    else:
        return Response(status=404)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_competitor_domains(request: Request):
    """
    Fetches blacklist for user's website
    """
    user: User = request.user
    website: Website = user.current_active_website

    if website:
        return JsonResponse(status=200,
                            data=sorted(list(website.competitor_set.all().values_list('domain', flat=True))), safe=False)

    return JsonResponse(status=200, data=[], safe=False)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def remove_competitors(request: Request):
    """
    Removes website competitors.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        domain_to_remove: str = request.data.get('domain', '').strip()
        keyword_project_id: str = request.data.get("projectId", "").strip()
    except KeyError:
        return JsonResponseBadRequest()

    if keyword_project_id:
        try:
            keyword_project_object = user.keyword_projects.get(
                project_id=keyword_project_id
            )
        except KeywordProject.DoesNotExist:
            logger.error(f"Keyword project with project Id  '{keyword_project_id}' was not found in user keyword Project")
            return JsonResponseBadRequest(
                additional_data={"err_id": "NO_SUCH_KEYWORD_FOUND"}
            )
        except Exception as err:
            logger.critical(err)
            return JsonResponseServerError()

        generated = user.articles.filter(keyword__in=keyword_project_object.keywords.all(), is_generated=True).exists()

        exists_and_off = user.automation_projects.filter(
            associated_keyword_project=keyword_project_object,
            auto_publish_state='off'
        ).exists()

        # Check if no project exists for the given keyword project
        no_projects_exist = not user.automation_projects.filter(
            associated_keyword_project=keyword_project_object
        ).exists()
        if not generated and (exists_and_off or no_projects_exist):
            try:
                website.competitor_set.filter(domain=domain_to_remove).delete()
                website.save()
                keyword_project_object.delete()
                return JsonResponse({"message": "Competitor deleted successfully"}, status=200)
            except Exception as e:
                return JsonResponse({"error":"Bad Request","message": "Competitor to delete not found"}, status=400)
        else:
            return JsonResponse({'message': "The competitor cannot be deleted because an Article or Auto Blog is already generated inside this competitor."},status=200)
    else:
        website.competitor_set.filter(domain=domain_to_remove).delete()
        website.save()

    return JsonResponse(status=200, data={'message': "Competitor deleted successfully"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_competitors(request: Request):
    """
    Add website competitors.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        domains: List[str] = request.data['domains']
    except KeyError:
        return JsonResponseBadRequest()

    competitors_to_ignore: List[str] = list(IgnoredCompetitor.objects.all().values_list('domain', flat=True))
    # clean each domain and ignore bad ones
    cleaned_domains: List[str] = []
    for domain in domains:
        domain_data = tldextract.extract(html.escape(domain))
        domain = domain_data.domain
        suffix = domain_data.suffix

        if suffix and domain and (domain not in competitors_to_ignore):
            cleaned_domains.append(f"{domain}.{suffix}")

    # # remove duplicates
    # # we do this here so that any duplicates resulting from subdomains are also considered.
    # cleaned_domains = list(set(cleaned_domains))

    # Remove competitors that are already present.
    cleaned_domains = list(
        set(cleaned_domains) - set(list(website.competitor_set.all().values_list('domain', flat=True)))
    )

    # fetch and store logos for these domains
    celery_save_logos.delay(cleaned_domains)

    # Create and add the competitors
    for domain in cleaned_domains:
        comp = Competitor(
            website=website,
            domain=domain,
            protocol="https",
            logo_url=f"https://{os.environ['CLOUDFLARE_R2_DOMAIN']}/{os.environ['CLOUDFLARE_R2_ENV']}/logo/{domain}"
        )
        comp.save()

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_competitor(request: Request):
    """
    Adds competitors to user's website.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        comp_domain: str = html.escape(request.data['domain']).lower().strip()
    except KeyError:
        return JsonResponseBadRequest()

    try:
        comp = Competitor.objects.get(domain=comp_domain)
        website.competitors.add(comp)
    except Competitor.DoesNotExist:
        name = comp_domain.split(".")[0].strip().capitalize()
        comp = Competitor(
            name=name,
            domain=comp_domain,
            protocol="https",
            logo_url=f"https://logo.clearbit.com/{comp_domain}",
        )
        comp.save()

    # Get keywords
    keywords: List[str] = list(map(lambda x: x['keyword'], get_keywords_from_domain(comp_domain)['data']))
    existing_keywords: List[str] = list(
        Keyword.objects.filter(keyword__in=keywords).distinct().values_list('keyword', flat=True)
    )
    new_keywords: List[str] = list(set(keywords) - set(existing_keywords))

    # For existing keywords, add them to competitor
    add_existing_keywords(comp,
                          existing_keywords,
                          "global",
                          "keywordseverywhere")

    # For new keywords, get additional data, create and then add them to competitor.
    additional_data: List[Dict] = get_keyword_volume_data(new_keywords)
    new_keywords_data: List[Dict] = []
    for data in additional_data:
        # so we won't save KWs with 0 volumes
        if not data["vol"]:
            continue
        m = hashlib.md5()
        m.update((data['keyword'] + 'global' + 'keywordseverywhere').encode('utf-8'))
        md5_hash: str = m.hexdigest()
        new_keywords_data.append({
            'md5_hash': md5_hash,
            'keyword': data['keyword'],
            'source': "keywordseverywhere",
            'country': "global",
            'serp_position': None,
            'volume': data['vol'],
            'cpc': {
                'currency': data['cpc']['currency'],
                'value': data['cpc']['value']
            },
            'paid_difficulty': data['competition'],
            'trend': data['trend'],
        })

    add_keywords(comp, new_keywords_data)

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_competitor_research_data(request: Request):
    """
    Fetches blacklist and some additional data for competitor research page.
    """
    user: User = request.user

    try:
        website = user.current_active_website
    except Website.DoesNotExist as err:
        logger.error("error in get_competitor_research_data()--", err)
        website = None

    if not website:
        return JsonResponse(status=200, data="No website found", safe=False)

    competitors: QuerySet[Competitor] = website.competitor_set.all().order_by('domain')

    # subquery to get HypestatData for each competitor
    hypestat_data_subquery = HypestatData.objects.filter(domain=OuterRef('domain'))

    # annotate competitors with HypestatData fields
    competitors = competitors.annotate(
        organic_traffic=Subquery(hypestat_data_subquery.values('organic_traffic')[:1]),
        organic_keywords=Subquery(hypestat_data_subquery.values('organic_keywords')[:1]),
        domain_authority=Subquery(hypestat_data_subquery.values('domain_authority')[:1]),
        total_backlinks=Subquery(hypestat_data_subquery.values('total_backlinks')[:1]),
        follow=Subquery(hypestat_data_subquery.values('follow')[:1]),
        no_follow=Subquery(hypestat_data_subquery.values('no_follow')[:1]),
        referring_domains=Subquery(hypestat_data_subquery.values('referring_domains')[:1]),
    )

    # content_plan_status = website.content_plan_generation_status
    data = [
        {
            'domain': comp.domain,
            'organic_traffic': comp.organic_traffic,
            'organic_keywords': comp.organic_keywords,
            'domain_authority': comp.domain_authority,
            'total_backlinks': comp.total_backlinks,
            'follow': comp.follow,
            'no_follow': comp.no_follow,
            'referring_domains': comp.referring_domains,
            'keywords_generated': comp.keywords_generated,
            'in_processing': comp.in_processing,
            'keyword_project_id': comp.associated_keyword_project.project_id if comp.associated_keyword_project else None,
            # 'content_plan_generation_status': content_plan_status,
        }
        for comp in competitors
    ]

    return JsonResponse(status=200, data=data, safe=False)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def bulk_archive_unarchive_articles(request: Request):
    """
    Archive the Articles
    """
    user: User = request.user

    try:
        articles_uid: str = request.data['articles_uid']
        archive_type: str = request.data['archive_type']
    except KeyError:
        return JsonResponseBadRequest()

    if archive_type == 'archive':
        archive = True
    elif archive_type == 'unarchive':
        archive = False
    else:
        return JsonResponseBadRequest()

    articles: QuerySet[Article] = user.articles.filter(
        article_uid__in=articles_uid
    )

    # exclude articles that are currently processing
    articles = articles.exclude(is_processing=True)

    if not articles.exists():
        return JsonResponse(
            status=400,
            data={'error': 'No valid articles found to update archive.'}
        )

    # mark the articles as archived
    articles.update(is_archived=archive)

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def generate_keywords_for_competitors(request: Request):
    """
    Unlocks competitor keywords.
    """
    user: User = request.user
    website = user.current_active_website

    try:
        competitor_domain: str = request.data['domain']
        selected_location = request.data['selectedLocation']
    except KeyError:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY"})

    try:
        comp: Competitor = website.competitor_set.get(domain=competitor_domain)
    except Competitor.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_COMPETITOR"})

    # Check if the user has reached the max limit for keywords
    max_keywords_allowed: int = get_stripe_product_data(user)['metadata']['max_keywords']
    if (user.keywords_generated) >= max_keywords_allowed:
        logger.error(f"generate_keywords_for_competitors() - Max limit reached for user {user.email}")
        return JsonResponse(status=200, data={"status": "rejected", 'reason': "max_limit_reached", 'message': 'max limit reached'})
    
    celery_get_competitors_keywords_from_domain.delay(user_email= user.email, competitor_domain = competitor_domain, selected_location=selected_location, max_keywords_allowed=max_keywords_allowed)

    comp.in_processing = True
    comp.save()
    
    # Return 200 response
    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_competitor_research_keywords_data(request: Request):
    """
    Returns keyword data for competitor research.

    :param request:
    :return:
    """
    user: User = request.user
    website = user.current_active_website

    try:
        competitor_domain: str = request.query_params['competitor_domain']
    except KeyError:
        return JsonResponseBadRequest(additional_data={'message': "MISSING_KEY"})

    try:
        comp: Competitor = website.competitor_set.get(domain=competitor_domain)
    except Competitor.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'message': "BAD_COMPETITOR_DOMAIN"})

    # Get md5 values of all keywords that are currently selected. This will be used to determine 'selected' bool
    # value in response data.
    # selected_keywords_hash_values: List[str] = list(
    #     website.selected_keywords.all().order_by('keyword').values_list('keyword_md5_hash', flat=True)
    # )

    response_data = {
        "keywords": [{
            "md5_hash": kw.keyword_md5_hash,
            "keyword": kw.keyword,
            "serp_position": kw.serp_position,
            "volume": kw.volume,
            "cpc_value": kw.cpc_value,
            "paid_difficulty": kw.paid_difficulty,
            "selected": kw.keyword_md5_hash,
        } for kw in comp.keywords.all()]
    }

    return JsonResponse(status=200, data=response_data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def delete_website_api(request: Request):
    """
    Deleted user's current active website.
    """
    user: User = request.user
        
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={"err_id": "NO_ACTIVE_WEBSITE"})

    # try:
    #     current_plan_name = get_stripe_product_data(user)['name']
    # except stripe.error.InvalidRequestError:
    #     current_plan_name = 'Trial'

    # if current_plan_name == 'Trial':
    #     return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    # Move user to another website in their account.
    other_websites = user.website_set.exclude(domain=website.domain)
    if other_websites:
        user.current_active_website = other_websites[0]
    else:
        user.current_active_website = None
    user.save()

    # Fetch All the webpages
    webpages = WebPage.objects.filter(website=website)

    # Delete ChromaDB data
    chroma_db_ids = list(webpages.values_list('embedding_id', flat=True))

    if chroma_db_ids:
        chromaDBManager = ChromaDBManager()
        chromaDBManager.delete_pages(chroma_db_ids, website.id)

    # Delete WebPages Data
    webpages.delete()

    # Delete this website
    website.delete()

    # Log the event
    add_website_log(
        user=user,
        domain=website.domain,
        message=f"{website.domain} disconnected by User.",
        connection_type='disconnected'
    )

    return JsonResponse(status=200, data={"message": "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def generate_v2_titles_from_keyword_api(request: Request):
    """
    Uploads keywords and location.

    :param request: Django Rest Framework's Request object
    """

    user: User = request.user

    if request.method == 'POST':
        try:
            keyword_hash: str = request.data['keyword_hash']
            location: str = request.data['location']
            count: int = int(request.data['count'])
        except KeyError:
            return JsonResponseBadRequest()

        max_titles_allowed: int = get_stripe_product_data(user)['metadata']['max_titles']

        if (user.titles_generated + count) > max_titles_allowed:
            return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_limit_reached'})

        try:
            # Queuing the Celery task to generate titless
            task = celery_generate_titles.delay(email=user.email, keyword_hash=keyword_hash, location=location, title_count=count)

            # Return the task id
            return JsonResponse(status=200, data={'status': "processing", 'task_id': task.id})
        except Exception as err:
            logger.exception(err)
            return JsonResponse(status=200, data={'status': "error"})
    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def generate_v2_titles_from_gsc_keyword_api(request: Request):
    """
    Uploads keywords and location.

    :param request: Django Rest Framework's Request object
    """

    user: User = request.user

    if request.method == 'POST':
        try:
            keyword: str = unescape_amp_char(request.data['keyword'])
            location: str = request.data['location']
            count: int = int(request.data['count'])
        except KeyError:
            return JsonResponseBadRequest()

        current_plan_data: Dict = get_stripe_product_data(user)
        max_titles_allowed: int = current_plan_data['metadata']['max_titles']
        max_keywords_allowed: int = current_plan_data['metadata']['max_keywords']

        if (user.keywords_generated) >= max_keywords_allowed:
            logger.error(f"generate_keywords_for_competitors() - Max limit reached for user {user.email}")
            return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_keywords_limit_reached'})

        if (user.titles_generated + count) > max_titles_allowed:
            return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_limit_reached'})

        # ============================================================================
        # ----------------------------- Save Keyword ---------------------------------
        # ============================================================================
        try:
            keyword_data_from_keywords_everywhere = get_keyword_volume_data([keyword], location.lower())

            # if there is no data for the keyword, add it with default values
            if not keyword_data_from_keywords_everywhere:
                keyword_object = create_keyword_object(user, keyword,
                                                       with_default_values=True,
                                                       country=location,
                                                       source="user-keyword")
                keyword_object.save()

            else:
                keyword_data = keyword_data_from_keywords_everywhere[0]
                keyword_object = create_keyword_object(user, keyword, **{
                    "source": "keywordseverywhere",
                    "country": location,
                    "serp_position": None,
                    "volume": keyword_data.get("vol", 0),
                    "cpc_currency": keyword_data.get("cpc", {}).get("currency", "USD"),
                    "cpc_value": keyword_data.get("cpc", {}).get("value", 1),
                    "paid_difficulty": keyword_data.get("competition", 0),
                    "trend": keyword_data.get("trend", [])
                })
                keyword_object.save()

            user.keywords_generated += 1
            user.total_keywords_generated += 1
            user.save()

        except Exception as err:
            logger.exception(err)
            return JsonResponseBadRequest(additional_data={'err_id': "keyword_save_error", 'message': err.__str__()})

        # ============================================================================
        # ----------------------------- Save Keyword Project -------------------------
        # ============================================================================
        try:
            project_name = f"Keyword-Project-GSC-{keyword}"
            keyword_project = KeywordProject.objects.create(
                website=user.current_active_website,
                project_name=project_name,
                project_id=str(uuid.uuid4())[:16],
                location_iso_code=location.lower()
            )
            keyword_project.keywords.add(keyword_object)
            keyword_project.total_traffic_volume = keyword_object.volume
            keyword_project.save()

            # =========================== Store Keyword Status ===========================
            keyword_status, created = GSCKeywordStatus.objects.get_or_create(
                keyword=keyword,
                website=user.current_active_website,
                defaults={"generated": False, "processing": True, "failed": False, "project_id": "","keyword_hash": ""}
            )

            # Update keyword_hash in keyword status
            keyword_status.keyword_hash = keyword_object.keyword_md5_hash
            # Update project_id in keyword status
            keyword_status.project_id = keyword_project.project_id
            keyword_status.save()

        except Exception as err:
            logger.critical(err)
            return JsonResponseBadRequest(additional_data={'err_id': "keyword_project_save_error", 'message': err.__str__()})

        # ============================================================================
        # ----------------------------- Generate TITLES -----------------------------
        # ============================================================================
        try:
            art = ArticleTitleGeneratorV2(user=user)
            title_list = art.generate_titles(keyword_object.keyword_md5_hash, location, count)

            if type(title_list) == str:
                return JsonResponseBadRequest(additional_data={'err_id': "title_gen_error", 'message': title_list})

            titles_generated = len(title_list)
            user.titles_generated += titles_generated
            user.total_titles_generated += titles_generated
            user.save()

            # Mark as generated
            keyword_status.generated = True
            keyword_status.processing = False
            keyword_status.save()

        except Exception as err:
            logger.exception(err)
            return JsonResponse(status=200, data={'status': "error"})

        return JsonResponse(status=200, data={'status': "success",'project_id':keyword_status.project_id,'keyword_hash':keyword_status.keyword_hash})

    else:
        return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# --------------------------- AI META DESCRIPTION GENERATOR TOOL -----------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_meta_desc_generator(request: Request):
    """
    Return AI response for AI Meta Description Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Meta-Description-Generator"
    if request.method == 'GET':
        try:
            keyword: str = request.GET.get('keyword', '').strip()

            # validate input
            valid = validate_input(keyword, 'Keyword', 3, 50)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

        except Exception as err:
            logger.error('Error in AI META DESCRIPTION GENERATOR tool--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'keyword': keyword}
        tool_prompt = meta_description_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.critical(err)
            return JsonResponse(status=500, data={
                'status': "error",
                'message': "Something went wrong try after sometime"
            })

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# --------------------------------- AI REWORDING TOOL ----------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_rewording(request: Request):
    """
    Return AI response for AI Rewording tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Rewording"
    if request.method == 'GET':
        try:
            content = str(request.GET.get('content', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(content, 'Content', 10, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count==0:
                return JsonResponse(status=400, data={'status': "error", 'message': "count should be in between 1 to 15."})

        except Exception as err:
            logger.error('Error in AI Rewording tool--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'content': content, 'count': count}
        tool_prompt = ai_rewording_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------------ AI BLOG TITLE GENERATOR -------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_blog_title_generator(request: Request):
    """
    Return AI response for AI Blog Title Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Blog-Title-Generator"
    if request.method == 'GET':
        try:
            blog_description = str(request.GET.get('blog_description', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(blog_description, 'Description', 15, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count == 0 or count > 15:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "title count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponseBadRequest()

        input_data = {'blog_description': blog_description, 'count': count}
        tool_prompt = ai_blog_title_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------------ AI CONTENT IDEA GENERATOR -----------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_content_idea_generator(request: Request):
    """
    Return AI response for AI Content Idea Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Content-Idea-Generator"
    if request.method == 'GET':
        try:
            title = str(request.GET.get('title', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(title, 'Title', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "idea count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'title': title, 'count': count}
        tool_prompt = ai_content_idea_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ---------------------------- AI SOCIAL MEDIA BIO GENERATOR ---------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_social_media_bio_generator(request: Request):
    """
    Return AI response for AI Social Media Bio Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Social-Media-Bio-Generator"
    if request.method == 'GET':
        try:
            description = str(request.GET.get('profile_description', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(description, 'Description', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "bio count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'description': description, 'count': count}
        tool_prompt = ai_social_media_bio_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------- AI SOCIAL MEDIA CAPTION GENERATOR --------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_social_media_caption_generator(request: Request):
    """
    Return AI response for AI Social Media Caption Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Social-Media-Caption-Generator"
    if request.method == 'GET':
        try:
            topic = str(request.GET.get('topic', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(topic, 'Topic', 5, 50)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "caption count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'topic': topic, 'count': count}
        tool_prompt = ai_social_media_caption_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# --------------------------- AI SOCIAL MEDIA HASHTAG GENERATOR ------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_social_media_hashtag_generator(request: Request):
    """
    Return AI response for AI Social Media Hashtag Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Social-Media-Hashtag-Generator"
    if request.method == 'GET':
        try:
            topic = str(request.GET.get('topic', '').strip().lower())
            count = int(request.GET.get('count', 10))

            pattern = re.compile(r'^[a-zA-Z0-9\ ]{3,30}$')
            if not pattern.match(topic):
                return JsonResponse(status=400, data={'status': "error", 'message': "Invalid topic"})

            if count > 15 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "hashtag count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'topic': topic, 'count': count}
        tool_prompt = ai_social_media_hashtag_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------- AI SOCIAL MEDIA USERNAME GENERATOR -------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_social_media_username_generator(request: Request):
    """
    Return AI response for AI Social Media Username Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Social-Media-Username-Generator"
    if request.method == 'GET':
        try:
            description = str(request.GET.get('profile_description', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(description, 'Description', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "username count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'description': description, 'count': count}
        tool_prompt = ai_social_media_username_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# -------------------------- AI YOUTUBE VIDEO TITLE GENERATOR --------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_youtube_video_title_generator(request: Request):
    """
    Return AI response for AI YouTube Video Title Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-YouTube-Video-Title-Generator"
    if request.method == 'GET':
        try:
            description = str(request.GET.get('description', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(description, 'Description', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 10 or count== 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "title count should be in between 1-10."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'description': description, 'count': count}
        tool_prompt = ai_youtube_video_title_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------------ AI BRAND NAME GENERATOR -------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_brand_name_generator(request: Request):
    """
    Return AI response for AI Brand Name Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Brand-Name-Generator"
    if request.method == 'GET':
        try:
            description = str(request.GET.get('description', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(description, 'Description', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "names count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'description': description, 'count': count}
        tool_prompt = ai_brand_name_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ----------------------------- AI BUSINESS NAME GENERATOR -----------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_business_name_generator(request: Request):
    """
    Return AI response for AI Business Name Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Business-Name-Generator"
    if request.method == 'GET':
        try:
            description = str(request.GET.get('description', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(description, 'Description', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "names count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'description': description, 'count': count}
        tool_prompt = ai_business_name_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# --------------------------- AI LINKEDIN POST IDEA GENERATOR --------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_li_posts_idea_generator(request: Request):
    """
    Return AI response for AI LinkedIn Post Idea Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-LinkedIn-Post-Idea-Generator"
    if request.method == 'GET':
        try:
            hook = str(request.GET.get('hook', '').strip().lower())
            count = int(request.GET.get('count', 5))

            # validate input
            valid = validate_input(hook, 'Hook', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 10 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "ideas count should be in between 1-10."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'hook': hook, 'count': count}
        tool_prompt = ai_li_post_idea_generator()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ---------------------------- AI COLOR PSYCHOLOGY GENERATOR ---------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_color_psy_generator(request: Request):
    """
    Return AI response for AI Color Psychology Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Color-Psychology-Generator"
    if request.method == 'GET':
        try:
            emotion = str(request.GET.get('emotion', '').strip().lower())
            count = int(request.GET.get('count', 10))

            # validate input
            valid = validate_input(emotion, 'Emotion', 3, 50)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 25 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "ideas count should be in between 1-25."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500, data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'emotion': emotion, 'count': count}
        tool_prompt = ai_color_psychology_generator()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def google_integration_fetch_connected_domains_api(request: Request):
    """
    API to fetch the data from integrated google APIs
    :param request: Django Rest Framework's Request object
    :return JsonResponse: Fetched data from google APIs
    """
    integration_type = "google-search-console"

    user: User = request.user
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponse(status=400, data={
            'message': "No website is connected",
            'success': False
        })

    integrated = getattr(website, f"{integration_type.replace('-', '_')}_integrated")

    if not integrated:
        return JsonResponseBadRequest(
            additional_data={
                "err_id": "NOT_INTEGRATED",
                "message": f"'{integration_type}' is not integrated.",
            }
        )

    try:
        credentials: Credentials = (
            google_integration_utils.get_google_oauth2_credentials(
                user, integration_type
            )
        )
        sites = google_integration_utils.fetch_sites_connected_to_gsc(credentials)

        return JsonResponse(status=200, data=sites['siteEntry'] if sites else [], safe=False)

    except RefreshError:
        return JsonResponseBadRequest(additional_data={'err_id': "ACCESS_REVOKED_OR_EXPIRED_TOKEN",
                                                       'message': "Access has been revoked by the user or the token has expired."})

    except Exception as err:
        logger.critical(err)
        return JsonResponseServerError()

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def google_integration_fetch_data_from_domain_api(request: Request):
    """
    API to fetch the data from integrated google APIs
    :param request: Django Rest Framework's Request object
    :return JsonResponse: Fetched data from google APIs
    """
    # Required data
    try:
        selected_domain: str = html.escape(request.query_params["selected_domain"]).strip().lower()
        duration: str = request.GET.get("duration", "Last 30 days").strip()
        realoadGSCInsights: bool = True if request.GET.get("realoadGSCInsights") == "true" else False
    except KeyError:
        return JsonResponseKeyError()

    integration_type = 'google-search-console'
    func_to_exec = 'fetch_search_performance_analytics_data'

        # Convert duration into actual date range
    duration_map = {
        "Last week": 7,
        "Last 30 days": 30,
        "Last 3 months": 90,
        "Last 1 year": 365,
    }

    days = duration_map.get(duration, 30)
    # Optional data
    params: Dict = request.data.get('params', {})
    if not params and integration_type == 'google-search-console' and func_to_exec == 'fetch_search_performance_analytics_data':
        # last 30 days; start & end date should be in YYYY-MM-DD format
        params = {
            'start_date': (datetime.datetime.now() - datetime.timedelta(days=days)).strftime('%Y-%m-%d'),
            'end_date': datetime.datetime.now().strftime('%Y-%m-%d')
        }
        logger.debug(f"google_integration_fetch_data_from_domain_api: Fetching {duration} keywords data from GSC.")

    if integration_type not in ['google-search-console', 'google-analytics', 'google-drive']:
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_INTEGRATION_TYPE",
                                                       'message': f"'{integration_type}' is not a valid integration type."})

    user: User = request.user
    website: Website = user.current_active_website

    integrated = getattr(website, f"{integration_type.replace('-', '_')}_integrated")

    if not integrated:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_INTEGRATED",
                                                       'message': f"'{integration_type}' is not integrated."})

    try:
        credentials: Credentials = google_integration_utils.get_google_oauth2_credentials(user, integration_type)
        site_url = google_integration_utils.get_site_name_on_gsc(credentials, selected_domain)

        if not site_url:
            return JsonResponse404(additional_data={'err_id': "SITE_NOT_FOUND",
                                                    'message': f"Currently active site '{selected_domain}' was not found on GSC."})

    except RefreshError:
        return JsonResponseBadRequest(additional_data={'err_id': "ACCESS_REVOKED_OR_EXPIRED_TOKEN",
                                                       'message': "Access has been revoked by the user or the token has expired."})

    gsc_data = get_or_add_gsc_insights_in_redis(user, credentials, params, site_url, days, realoadGSCInsights)
    
    if gsc_data.get('data'):
        return JsonResponse(status=200, data=gsc_data)
    
    else:
        return JsonResponse(status=500, data=gsc_data)

# --------------------------------------------------------------------------------------
# ---------------------------- AI JOB DESCRIPTION GENERATOR ----------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_job_desc_generator(request: Request):
    """
    Return AI response for AI Job Description Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Job-Description-Generator"
    if request.method == 'GET':
        try:
            job_title = str(request.GET.get('job_title', '').strip().lower())
            count = int(request.GET.get('count', 1))

            # validate input
            valid = validate_input(job_title, 'Job title', 3, 50)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 10 or count == 0:
                return JsonResponse(status=400, data={'status': "error", 'message': "count should be in between 1-10."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'job_title': job_title, 'count': count}
        tool_prompt = ai_job_desc_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# --------------------------------- AI STORY GENERATOR ---------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_story_generator(request: Request):
    """
    Return AI response for AI Story Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Story-Generator"
    if request.method == 'GET':
        try:
            story_topic = str(request.GET.get('story_topic', '').strip().lower())
            paragraphs = int(request.GET.get('paragraphs', 1))

            # validate input
            valid = validate_input(story_topic, 'Story topic', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if paragraphs > 10 or paragraphs == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "paragraphs count should be in between 1-10."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'story_topic': story_topic, 'paragraphs': paragraphs}
        tool_prompt = ai_story_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# --------------------------- AI BUSINESS DESCRIPTION GENERATOR ------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_business_desc_generator(request: Request):
    """
    Return AI response for AI Business Description Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Business-Description-Generator"
    if request.method == 'GET':
        try:
            business_name = str(request.GET.get('business_name', '').strip().lower())
            short_desc = str(request.GET.get('short_desc', '').strip().lower())

            count = int(request.GET.get('count', 1))

            # validate input
            valid = validate_input(business_name, 'Business Name', 3, 100)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            valid = validate_input(short_desc, 'Description', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 10:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "description count should be less than 10."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'business_name': business_name, 'short_description': short_desc, 'count': count}
        tool_prompt = ai_business_desc_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ---------------------------- AI BUSINESS SLOGAN GENERATOR ----------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_business_slogan_generator(request: Request):
    """
    Return AI response for AI Business Slogan Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Business-Slogan-Generator"
    if request.method == 'GET':
        try:
            business_name = str(request.GET.get('business_name', '').strip().lower())
            short_desc = str(request.GET.get('short_desc', '').strip().lower())

            count = int(request.GET.get('count', 1))

            # validate input
            valid = validate_input(business_name, 'Business Name', 3, 100)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            valid = validate_input(short_desc, 'Description', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 10:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "description count should be less than 10."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'business_name': business_name, 'short_description': short_desc, 'count': count}
        tool_prompt = ai_business_slogan_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ---------------------------------- AI NDA GENERATOR ----------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_nda_generator(request: Request):
    """
    Return AI response for AI NDA Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-NDA-Generator"
    if request.method == 'GET':
        try:
            disclosing_party = str(request.GET.get('disclosing_party', '').strip().lower())
            receiving_party = str(request.GET.get('receiving_party', '').strip().lower())

            valid = validate_input(disclosing_party, 'Disclosing party', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            valid = validate_input(receiving_party, 'Receiving party', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if disclosing_party == '' or receiving_party == '':
                return JsonResponse(status=400, data={'status': "error", 'message': "missing required data"})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'disclosing_party': disclosing_party, 'receiving_party': receiving_party}
        tool_prompt = ai_nda_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# --------------------------------- AI ESSAY GENERATOR ---------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_essay_generator(request: Request):
    """
    Return AI response for AI Story Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Essay-Generator"
    if request.method == 'GET':
        try:
            essay_topic = str(request.GET.get('essay_topic', '').strip().lower())
            paragraphs = int(request.GET.get('paragraphs', 1))

            valid = validate_input(essay_topic, 'Essay Topic', 5, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if paragraphs > 15 or paragraphs == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "paragraphs count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'essay_topic': essay_topic, 'paragraphs': paragraphs}
        tool_prompt = ai_essay_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# -------------------------------- AI KEYWORD GENERATOR --------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_keyword_generator(request: Request):
    """
    Return AI response for AI Keyword Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Keyword-Generator"
    if request.method == 'GET':
        try:
            keyword = str(request.GET.get('keyword', '').strip())
            count = int(request.GET.get('count', 5))

            valid = validate_input(keyword, 'Keyword', 3, 50)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 25 or count == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "keyword count should be in between 1-25."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'keyword': keyword, 'count': count}
        tool_prompt = ai_keyword_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ----------------------------- AI VIDEO SCRIPT GENERATOR ------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_video_script_generator(request: Request):
    """
    Return AI response for AI Video Script Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Video-Script-Generator"
    if request.method == 'GET':
        try:
            short_story = str(request.GET.get('short_story', '').strip().lower())
            duration = int(request.GET.get('duration', 5))

            valid = validate_input(short_story, 'Short story', 15, 500)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if duration > 60 or duration == 0:
                return JsonResponse(status=400,
                                    data={'status': "error", 'message': "duration should be less than 60 minutes."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'short_story': short_story, 'duration': duration}
        tool_prompt = ai_video_script_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------------ DOMAIN AUTHORITY CHECKER ------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def domain_authority_checker(request: Request):
    """
    Return AI response for Domain Authority Checker tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: HypeStat response
    """

    # tool_name = "Domain-Authority-Checker"
    if request.method == 'GET':
        try:
            input_domain = str(request.GET.get('domain', '').strip().lower())
            # domains = [domain.strip().lower() for domain in input_domains]

            if not input_domain.startswith(('http://', 'https://')):
                input_domain = 'http://' + input_domain
            parsed_url = urlparse(input_domain)
            domain = parsed_url.netloc

            if domain == '':
                return JsonResponse(status=400, data={'status': "error", 'message': "Invalid Domain"})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})
        try:
            result = HypestatData.objects.filter(domain=domain)

            if len(result) == 0:
                result = fetch_hypestat_data([domain])[0]

                hype = HypestatData(
                    domain=result['domain'],
                    organic_traffic=result['organic_traffic'],
                    organic_keywords=result['organic_keywords'],
                    domain_authority=result['domain_authority'],
                    total_backlinks=result['total_backlinks'],
                    follow=result['follow'],
                    no_follow=result['no_follow'],
                    referring_domains=result['referring_domains'],
                )
                hype.save()

            else:
                result = json.loads(serialize('json', result))[0]['fields']
            return JsonResponse(status=200, data={'status': "success", 'data': result})

        except IntegrityError as err:
            print(f"[*] Could not save hypestat data for {result['domain']}: {err}")
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------- AI BLOG META DESCRIPTION GENERATOR -------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_blog_meta_desc_generator(request: Request):
    """
    Return AI response for AI Blog Meta Description Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Blog-Meta-Description-Generator"
    if request.method == 'GET':
        try:
            title = str(request.GET.get('title', '').strip().lower())
            count = int(request.GET.get('count', 1))

            # validate input
            valid = validate_input(title, 'Blog title', 10, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400, data={'status': "error", 'message': "count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'title': title, 'count': count}
        tool_prompt = ai_blog_meta_desc_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ---------------------------- AI ARTICLE OUTLINE GENERATOR ----------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_article_outline_generator(request: Request):
    """
    Return AI response for AI Article Outline Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Article-Outline-Generator"
    if request.method == 'GET':
        try:
            title = str(request.GET.get('title', '').strip().lower())
            count = int(request.GET.get('count', 1))

            # validate input
            valid = validate_input(title, 'Blog title', 10, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count < 3:
                return JsonResponse(status=400, data={'status': "error", 'message': "count should be in between 3-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'title': title, 'count': count}
        tool_prompt = ai_article_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


# --------------------------------------------------------------------------------------
# ------------------------------ AI COMPANY BIO GENERATOR ------------------------------
# --------------------------------------------------------------------------------------
@api_view(['GET'])
@cors_allow_all
def ai_company_bio_generator(request: Request):
    """
    Return AI response for AI Company Bio Generator tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    tool_name = "AI-Company-Bio-Generator"
    if request.method == 'GET':
        try:
            company_name = str(request.GET.get('company_name', '').strip().lower())
            company_desc = str(request.GET.get('company_desc', '').strip().lower())

            count = int(request.GET.get('count', 1))

            # validate input
            valid = validate_input(company_name, 'Company name', 3, 50)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            # validate input
            valid = validate_input(company_desc, 'Company description', 10, 150)
            if not valid[0]:
                return JsonResponse(status=400, data={'status': "error", 'message': valid[1]})

            if count > 15 or count == 0:
                return JsonResponse(status=400, data={'status': "error", 'message': "count should be in between 1-15."})

        except Exception as err:
            logger.error('Key error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

        input_data = {'company_name': company_name, 'company_description': company_desc,'count': count}
        tool_prompt = ai_company_bio_generator_prompt()

        try:
            prompt = PromptTemplate.from_template(tool_prompt)
            llm = ChatOpenAI(
                model_name=get_gpt_model_name(),
                openai_api_key=MINI_AI_TOOL_OPENAI_API_KEY,
                temperature=0.3
            )

            chain = prompt | llm
            response = chain.invoke(input_data)

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            result = response.content.strip().replace("```", "")
            if result.lower().startswith("json"):
                result = result[4:].lstrip()

            add_ai_tool_result_in_redis(tool_name, result)
            data = json.loads(result)

            return JsonResponse(status=200, data={'status': "success", 'data': data})

        except Exception as err:
            logger.error('error--', err)
            return JsonResponse(status=500,
                                data={'status': "error", 'message': "Something went wrong try after sometime"})

    return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def ai_keywords_research_api(request: Request):
    """
    Return AI response for Get Related Keywords tool.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """

    class GetRelatedKeywords(BaseModel):
        AllRelatedkeywords: List[str] = Field(description="A list of related keywords based on the input keyword")

    user: User = request.user

    try:
        # Check if the user has reached the max limit for keywords
        max_keywords_allowed: int = get_stripe_product_data(user)['metadata']['max_keywords']
        if user.keywords_generated >= max_keywords_allowed:
            return JsonResponse(status=200, data={"status": "rejected", 'reason': "max_limit_reached", 'message': 'max limit reached'})
    except Exception as err:
        logger.error(f"ai_keywords_research_api() - Error while checking max limit for user {user.email}: {err}")
        return JsonResponseServerError()

    try:
        keywords = unescape_amp_char(request.data['keywords'])
        selected_location = request.data['selectedLocation']

    except KeyError:
        return JsonResponseBadRequest()

    project_name = "AI-Keyword-" + keywords[0][:15]

    if not keywords:
        return JsonResponseBadRequest()

    # Check block keywords list
    block_keywords = BlockKeywords.objects.all().values_list('keyword', flat=True)
    for keyword in keywords:
        if any(block_kw in keyword for block_kw in block_keywords):
            return JsonResponse(status=200, data={
                "status": "rejected",
                "reason": "blocked_keyword_used",
                "message": f"You cannot add '{keyword}' keyword. If you feel this is incorrect, please contact us via live chat."
            })

    # Get related keywords using GPT-4o-mini model for each keyword
    all_keywords = [keyword for keyword in keywords] # add the original keywords
    for keyword in keywords:
        llm = ChatOpenAI(
            model_name=get_gpt_model_name(),
            temperature=0.7,
        )

        parser = PydanticOutputParser(pydantic_object=GetRelatedKeywords)
        retry_parser = RetryWithErrorOutputParser.from_llm(parser=parser, llm=llm)
        prompt = PromptTemplate(
            template=get_related_keywords_prompt(),
            input_variables=['keyword'],
            partial_variables={"format_instructions": parser.get_format_instructions()}
        )

        _input = prompt.format_prompt(keyword=keyword)
        _response = llm.invoke(_input.to_string())
        _output = _response.content

        total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
        add_or_update_gpt_token_usage(total_tokens)

        try:
            structured_output = parser.parse(_output)
        except OutputParserException:
            logger.debug("ICP output parser failed. Retrying...")
            structured_output = retry_parser.parse_with_prompt(_output, _input)

        # remove duplicates
        for k in structured_output.AllRelatedkeywords:
            if k not in all_keywords:
                all_keywords.append(k)

    # do keyword research
    keywords_data_from_keywords_everywhere = get_keyword_volume_data(all_keywords, selected_location['country_iso_code'].lower())

    new_keywords_with_data = []
    for keyword_data in keywords_data_from_keywords_everywhere:
        cleaned_keyword: str = re.sub(r'[^\w\s]', "", keyword_data['keyword'])


        # if there is no data for the keyword, add it with default values
        if not keyword_data["vol"]:
            keyword_obj = create_keyword_object(user, cleaned_keyword,
                                                with_default_values=True,
                                                country=selected_location['country_iso_code'],
                                                source="user-keyword")
            new_keywords_with_data.append(keyword_obj)
            continue

        keyword_obj = create_keyword_object(user, cleaned_keyword, **{
            "source": "keywordseverywhere",
            "country": selected_location['country_iso_code'],
            "serp_position": None,
            "volume": keyword_data["vol"],
            "cpc_currency": keyword_data['cpc']['currency'],
            "cpc_value": keyword_data['cpc']['value'],
            "paid_difficulty": keyword_data["competition"],
            "trend": keyword_data["trend"]
        })
        new_keywords_with_data.append(keyword_obj)

    if new_keywords_with_data.__len__() == 0:
        return JsonResponse(status=200, data={
            "status": "rejected",
            "reason": "no_keywords_found",
            "message": "No keywords found. Please try with different keywords."
        })

    try:
        if (user.keywords_generated + len(new_keywords_with_data)) > max_keywords_allowed:
            # If the total number of keywords generated by the user and the new keywords exceed the max limit, then limit the number of keywords to be generated
            new_keywords_with_data = new_keywords_with_data[:max_keywords_allowed - user.keywords_generated]

        # Create a new KeywordProject instance
        keyword_project = KeywordProject.objects.create(
            website=user.current_active_website,
            project_name=project_name,
            project_id=str(uuid.uuid4())[:16],
            location_iso_code=selected_location['country_iso_code'].lower(),
        )

        # save all keywords to the database
        with transaction.atomic(using='default'):
            # Skip keyword if it already exists in the database
            Keyword.objects.bulk_create(new_keywords_with_data , ignore_conflicts=True)
            user.keywords_generated += len(new_keywords_with_data)
            user.total_keywords_generated += len(new_keywords_with_data)
            user.save()

        # Add all new keywords to the keyword project
        with transaction.atomic(using='default'):
            keyword_project.keywords.add(*new_keywords_with_data)

        # update the total traffic volume
        keyword_project.total_traffic_volume = sum([kw.volume for kw in new_keywords_with_data])
        keyword_project.save()

    except Exception as err:
        logger.exception(err)
        return JsonResponseBadRequest()

    return JsonResponse(status=200, data={'related_keywords': all_keywords})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def save_automation_project_api(request: Request):
    """
    Save automation project
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['display_name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if 'Trial' in current_plan_name:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_ALLOWED", 'message': "API is not allowed on Trial Plan."})

    try:
        automation_project_data = request.data
    except KeyError:
        logger.error("save_automation_project_api() error - Missing key")
        return JsonResponseBadRequest()

    logger.info(f"save_automation_project_api() - {automation_project_data}")

    try:
        selected_keyword_project_id = automation_project_data.get("selectedKeywordProjectId")

        if not selected_keyword_project_id:
            logger.error("save_automation_project_api() error - 'selectedKeywordProjectId' is missing in automation_project_data")
            return JsonResponse({"error": "Missing 'selectedKeywordProjectId' in request data"}, status=400)

        associated_keyword_project = user.keyword_projects.get(
            project_id=selected_keyword_project_id
        )

    except KeywordProject.DoesNotExist:
        logger.error("save_automation_project_api() error - No such keyword project")

        ## randomly select a keyword project
        associated_keyword_project = user.keyword_projects

        if len(associated_keyword_project) == 0:
            return JsonResponseBadRequest({"err_id": "NO_KEYWORD_PROJECTS", 'message': f"No keyword projects found."})

        associated_keyword_project = random.choice(associated_keyword_project)

    logger.info(f"save_automation_project_api() - {associated_keyword_project}")

    if not automation_project_data:
        return JsonResponseBadRequest()

    try:
        automation_project = AutomationProject(
            website=user.current_active_website,
            project_id=str(uuid.uuid4())[:16],
        )
        automation_project.project_name = f"Automation Project - {unescape_amp_char(associated_keyword_project.project_name)}"
        automation_project.associated_keyword_project = associated_keyword_project
        automation_project.keywords_traffic_range_min = automation_project_data["traffic_range"][0]
        automation_project.keywords_traffic_range_max = automation_project_data["traffic_range"][1]
        automation_project.frequency = automation_project_data["frequency"]
        automation_project.article_count = automation_project_data["articles_count"]
        automation_project.selected_integration_name = automation_project_data["integration"]
        automation_project.selected_integration_unique_text_id = automation_project_data["integration_unique_id"]
        automation_project.auto_publish_days = automation_project_data["publish_days"]
        automation_project.auto_publish_time = automation_project_data["publish_time"]
        automation_project.publish_only_generated_articles = automation_project_data["publish_only_generated_articles"]
        automation_project.auto_publish_state = automation_project_data["publish_state"]
        automation_project.save()
    except Exception as err:
        logger.exception(f"Error saving automation project: {err}")
        return JsonResponseBadRequest()

    return JsonResponse(status=200, data={"success": True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_automation_project_api(request: Request):
    """
    Update automation project
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['display_name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if 'Trial' in current_plan_name:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_ALLOWED", 'message': "API is not allowed on Trial Plan."})

    try:
        automation_project_data = request.data
        automation_project = user.automation_projects.get(
            project_id=automation_project_data["automation_project_id"]
        )

    except KeyError:
        logger.error("update_automation_project_api() error - Missing key")
        return JsonResponseBadRequest()

    except AutomationProject.DoesNotExist:
        logger.error("update_automation_project_api() error - No such automation project")
        return JsonResponseBadRequest(
            additional_data={"err_id": "NO_SUCH_AUTOMATION_PROJECT"}
        )

    if not automation_project_data:
        logger.error("update_automation_project_api() error - No data")
        return JsonResponseBadRequest()

    try:
        automation_project.keywords_traffic_range_min = automation_project_data["trafficRangeMin"]
        automation_project.keywords_traffic_range_max = automation_project_data["trafficRangeMax"]
        automation_project.frequency = automation_project_data["frequency"]
        automation_project.article_count = automation_project_data["articles_count"]
        automation_project.selected_integration_name = automation_project_data["integration"]
        automation_project.selected_integration_unique_text_id = automation_project_data["integration_unique_id"]
        automation_project.auto_publish_days = automation_project_data["publish_days"]
        automation_project.auto_publish_time = automation_project_data["publish_time"]
        automation_project.publish_only_generated_articles = automation_project_data["publish_only_generated_articles"]
        automation_project.auto_publish_state = automation_project_data["publish_state"]
        automation_project.save()
    except Exception as err:
        logger.exception(err)
        return JsonResponseBadRequest()

    return JsonResponse(status=200, data={"success": True})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def delete_automation_project_api(request: Request):
    """
    delete automation project
    :param request: Django Rest Framework's Request object
    :return JsonResponse: AI response
    """
    user: User = request.user
    project_id: str = request.data['project_id']

    try:
        current_plan_name = get_stripe_product_data(user)['display_name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if 'Trial' in current_plan_name:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_ALLOWED", 'message': "API is not allowed on Trial Plan."})

    if not project_id:
        return JsonResponse({'error': 'Project ID is required'}, status=400)

    try:
        automation_project = user.automation_projects.get(project_id=project_id)

        if not automation_project:
            logger.error(f"delete_automation_project_api() - AutomationProject not found")
            return JsonResponse({'message': 'AutomationProject not found'}, status=404)

        automation_project.delete()

    except Exception as err:
        logger.exception(err)
        return JsonResponseBadRequest()

    return JsonResponse(status=200, data={"success": True})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def clearbit_domain_suggestion(request: Request):
    """
    Clearbit Domain Suggestion API
    :param request: Django Rest Framework's Request object
    """
    try:
        website_name: str = html.escape(request.query_params['name']).strip().lower()
    except KeyError as key:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY", 'message': f"{key} is missing."})

    # Get the domains
    try:
        res = requests.get(f"https://autocomplete.clearbit.com/v1/companies/suggest?query={website_name}", timeout=10)
        clearbit_response_data: Dict = res.json()
        return JsonResponse(status=200, data={'success': True, 'suggestions': clearbit_response_data})

    except requests.exceptions.Timeout:
        return JsonResponse(status=500, data={'success': False, 'suggestions': [], 'error_reason': "Clearbit API timeout."})

    except Exception as err:
        logger.error(f"Clearbit API error: {err}")
        return JsonResponse(status=500, data={'success': False, 'domains': [], 'error_reason': "Clearbit API error."})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_google_suggestions(request: Request):
    """
    Get Google Suggestions API
    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    location_data = user.latest_ip_info

    try:
        keyword = html.escape(request.query_params["keyword"]).strip()
    except KeyError as key:
        return JsonResponseBadRequest(
            additional_data={"err_id": "MISSING_KEY", "message": f"{key} is missing."}
        )

    country_code = location_data.get(
        "country", "us"
    ).lower()  # Default to 'us' if country is not found
    language_code = location_data.get(
        "country", "en"
    ).lower()  # Default to 'en' if language is not found

    url = "http://suggestqueries.google.com/complete/search"
    params = {
        "client": "firefox",
        "q": keyword,
        "gl": country_code,
        "hl": language_code,
    }

    # Get the suggestions
    try:
        response = requests.get(url, params=params, timeout=10)
        response.raise_for_status()
        suggestions = json.loads(response.text)
        keywords = suggestions[1]
        return JsonResponse(status=200, data={"success": True, "suggestions": keywords})

    except requests.exceptions.Timeout:
        return JsonResponse(
            status=500,
            data={
                "success": False,
                "suggestions": [],
                "error_reason": "Google API timeout.",
            },
        )

    except Exception as err:
        logger.error(f"Google API error: {err}")
        return JsonResponse(
            status=500,
            data={
                "success": False,
                "suggestions": [],
                "error_reason": "Google API error.",
            },
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def feature_request_api(request: Request):
    """
    Sends User feature request message to admins
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    try:
        message: str = request.data['message']
    except KeyError:
        return JsonResponseBadRequest()

    # Send email message to admins
    send_email([ADIL_EMAIL, JD_EMAIL, AMIN_EMAIL],
               ABUN_NOTIFICATION_EMAIL,
               "Team Abun",
               f"Receive Feature Request From - {user.email}",
               message,
               reply_to=user.email)

    # Send acknowledgment email to the user
    send_email(user.email,
               ABUN_NOTIFICATION_EMAIL,
               "Team Abun",
               f"Your Feature Request Has Been Received",
               feature_request_email_body(user.username))

    return JsonResponse(status=200, data={'message': "Thank you for your request. We will review it and respond as soon as possible.",
                                          'success': True})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def integration_feature_request_api(request: Request):
    """
    Sends User integration feature request message to admins
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    try:
        message: str = request.data['message']
    except KeyError:
        return JsonResponseBadRequest()

    # Send email message to admins
    send_email([ADIL_EMAIL, JD_EMAIL, AMIN_EMAIL, ABUN_INFO_EMAIL],
               ABUN_NOTIFICATION_EMAIL,
               "Team Abun",
               f"New Integration Request Received - {user.email}",
               message,
               reply_to=user.email)

    # Send acknowledgment email to the user
    send_email(user.email,
               ABUN_NOTIFICATION_EMAIL,
               "Team Abun",
               f"Got it! Your Feature Request is in Review 🚀",
               feature_request_email_body(user.username))

    return JsonResponse(status=200, data={'message': "Got your request! We'll check it out and get back to you soon.",
                                          'success': True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def schedule_article_auto_publish(request: Request):
    """
    Schedules an article for auto publish
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    try:
        article_uid: str = request.data['article_uid']
        schedule_datetime_string: str = request.data['schedule_date']
        selected_integration: str = request.data['selected_integration']
        selected_integration_unique_id: str = request.data.get('selected_integration_id')
        post_status: Literal['draft', 'publish'] = request.data.get('post_status', 'publish')

    except KeyError:
        return JsonResponseBadRequest()

    try:
        if schedule_datetime_string.endswith("Z"):
            schedule_datetime_string = schedule_datetime_string.replace("Z", "+00:00")

        schedule_datetime = datetime.datetime.fromisoformat(schedule_datetime_string)
        current_datetime = datetime.datetime.now(tz=ZoneInfo('UTC'))
    except (ValueError, TypeError):
        return JsonResponse(status=200, data={'success': False,
                                              'message': "Schedule date is not in valid ISO format."})

    if selected_integration not in user.all_integrations:
        return JsonResponse(status=200, data={'success': False, 'message': f"Please connect your '{selected_integration}' "
                                                                           "account to schedule an article for publishing."})


    elif current_datetime > schedule_datetime or (schedule_datetime - current_datetime).days > 100:
        return JsonResponse(status=200, data={'success': False, 'message': f"Please select a valid schedule date & time."})

    try:
        article = user.articles.get(article_uid=article_uid)
    except Article.DoesNotExist:
        return JsonResponse(status=200, data={'success': False, 'message': f"No article found with '{article_uid}' article UID."})

    if not article.user.email == user.email:
        logger.critical(f"'{article_uid}' belongs to the user '{article.user.email}', but "
                        f"'{user.email}' tried to schedule this article for auto publish.")
        return JsonResponse(status=400, data={'success': False, 'message': f"No article found with '{article_uid}' article UID."})

    elif article.is_posted:
        return JsonResponse(status=200, data={'success': False, 'message': f"Article is already published to your site."})

    schedule_article_posting, _ = ScheduleArticlePosting.objects.get_or_create(article=article)
    schedule_article_posting.schedule_datetime = schedule_datetime
    schedule_article_posting.selected_integration_name = selected_integration
    schedule_article_posting.selected_integration_unique_text_id = selected_integration_unique_id
    schedule_article_posting.post_status = post_status
    schedule_article_posting.save()

    return JsonResponse(status=200, data={'success': True, 'message': "OK", "selected_integration": selected_integration})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def fetch_scheduled_articles(request: Request):
    """
    Fetches all the scheduled articles for auto publish
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    scheduled_articles = ScheduleArticlePosting.objects.filter(
        article__website=user.current_active_website
    ).values(
        "article__article_uid", "article__title", "schedule_datetime", "schedule_on"
    )
    scheduled_articles_data = []
    non_scheduled_articles_data = []

    for scheduled_article in scheduled_articles:
        scheduled_articles_data.append(
            {
                "article_uid": scheduled_article["article__article_uid"],
                "title": scheduled_article["article__title"],
                "schedule_datetime": scheduled_article["schedule_datetime"].astimezone(tz=ZoneInfo(user.user_tz)).strftime("%d %B, %Y, %H:%M %Z"),
                "schedule_on": scheduled_article["schedule_on"],
            }
        )

    # also get non-scheduled articles
    non_scheduled_articles = user.articles.filter(is_posted=False, is_generated=True).values(
        "article_uid", "title"
    )

    for non_scheduled_article in non_scheduled_articles:
        # making sure to exclude the scheduled articles
        if non_scheduled_article["article_uid"] in [article["article_uid"] for article in scheduled_articles_data]:
            continue
        non_scheduled_articles_data.append(
            {
                "article_uid": non_scheduled_article["article_uid"],
                "article_title": non_scheduled_article["title"]
            }
        )

    return JsonResponse(
        status=200,
        data={
            "scheduled_articles": scheduled_articles_data,
            "non_scheduled_articles": non_scheduled_articles_data,
            "message": "OK",
        },
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def get_generated_articles_for_automation_project(request: Request):
    """
    Fetches all the generated articles for all keywords of the selected keyword project
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['display_name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if 'Trial' in current_plan_name:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_ALLOWED", 'message': "API is not allowed on Trial Plan."})

    try:
        keyword_project_id: str = request.query_params['keyword_project_id']
        automation_project_id: str = request.query_params['automation_project_id']
    except KeyError:
        return JsonResponseBadRequest()

    try:
        automation_project: AutomationProject = user.automation_projects.get(project_id=automation_project_id)
    except AutomationProject.DoesNotExist:
        # User is creating a new automation project
        automation_project = None

    if not automation_project:
        try:
            keyword_project: KeywordProject = user.keyword_projects.get(project_id=keyword_project_id)
        except KeywordProject.DoesNotExist:
            return JsonResponseBadRequest(additional_data={'err_id': "KEYWORD_PROJECT_NOT_FOUND", 'message': "Keyword project not found."})
    else:
        keyword_project = automation_project.associated_keyword_project

    try:
        all_keywords_data = []
        all_generated_articles = []

        for keyword in keyword_project.keywords.all():
            all_keywords_data.append({
                "keyword": keyword.keyword,
                "volume": keyword.volume,
            })

        if automation_project:
            for article in automation_project.article_set.all():
                all_generated_articles.append(
                    {
                        "article_uid": article.article_uid,
                        "title": article.title,
                        "keyword": keyword.keyword,
                        "keyword_volume": keyword.volume,
                        "posted_on": article.posted_on,
                        "scheduled_on": "",  # todo
                        "word_count": article.word_count,
                        "isPosted": article.is_posted,
                        "isGenerated": article.is_generated,
                        "postedTo": article.posted_to,
                        "postLink": article.article_link
                    }
                )

        return JsonResponse(
            status=200,
            data={
                "generated_articles": all_generated_articles,
                "associated_keywords": all_keywords_data,
                "message": "OK",
            },
        )

    except Exception as err:
        logger.critical(err)
        return JsonResponseBadRequest(additional_data={'err_id': "INTERNAL_SERVER_ERROR", 'message': "Internal server error."})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def wix_integration_auth(request: Request):
    """
    Authorize wix api token & site id

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    # do not proceed if user is not on a paid plan
    try:
        current_plan = get_stripe_product_data(user)
        current_plan_name = current_plan['name']
    except stripe.error.InvalidRequestError:
        current_plan = None
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    wix_website_limit = current_plan['metadata']['websites']

    if user.wix_integrations.count() >= wix_website_limit:
        return JsonResponse(status=200, data={
            "success": False,
            "message": "Your Wix sites limit has been reached. Please upgrade your plan to increase the limit."
        })

    try:
        token: str = request.data["token"]
        site_id: str = request.data["site_id"]
    except KeyError:
        return JsonResponseKeyError()

    if user.wix_integrations.filter(site_id=site_id).exists():
        return JsonResponse(status=200, data={
            'success': False,
            'message': f"This site is already connected."
        })

    # Use members endpoint for authenticating the api & fetching the member ID
    url = "https://www.wixapis.com/members/v1/members"
    headers = {
        'Content-Type': 'application/json',
        'Authorization': token,
        'wix-site-id': site_id
    }

    try:
        res = requests.get(url, headers=headers)

    except requests.exceptions.SSLError as err:
        logger.error(err)
        return JsonResponse(status=200, data={
            'success': False,
            'message': "Failed to verify your SSL certificate. Please try again after sometime."
        })

    except requests.exceptions.ConnectionError as err:
        logger.error(err)
        return JsonResponse(status=200, data={'success': False, 'message': "Failed to establish connection with WIX API. Please try again after sometime."})

    if not res.ok:
        return JsonResponse(status=200, data={'success': False, 'message': "We were unable to authenticate your WIX API key & Site ID. "
                                                                           "Please make sure all necessary permissions are allowed "
                                                                           "and API key & Site ID is valid."})

    try:
        members = res.json()["members"]
    except KeyError:
        return JsonResponse(status=200, data={'success': False, 'message': "Failed to find the Member ID. Please try with another API."})

    if not members:
        return JsonResponse(status=200, data={'success': False, 'message': "Failed to find the Member ID. Please try with another API."})


    # Fetch the list of sites associated with the token
    sites_url = "https://www.wixapis.com/site-list/v2/sites/query"
    sites_res = requests.post(sites_url, headers=headers)

    if not sites_res.ok:
        return JsonResponse(status=200, data={'success': False, 'message': "Failed to fetch Wix sites."})

    try:
        sites_data = sites_res.json()["sites"]
    except KeyError:
        return JsonResponse(status=200, data={'success': False, 'message': "Failed to fetch sites data from Wix."})

    site_url = None

    for site in sites_data:
        if site["id"] == site_id:
            site_url = site["viewUrl"]
            break

    add_wix_integration(
        user,
        token,
        site_id,
        members[0]['id'],
        site_url,
    )

    return JsonResponse(status=200, data={'success': True, 'message': "OK"})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def ghost_integration_auth(request: Request):
    """
    Authorize Ghost CMS API key & site URL.

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    # Check if the user is on a paid plan
    try:
        current_plan = get_stripe_product_data(user)
        current_plan_name = current_plan['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponse({'success': False, 'message': 'Upgrade your plan to integrate Ghost CMS.'}, status=400)

    ghost_website_limit = current_plan['metadata']['websites']

    if user.ghost_integrations.count() >= ghost_website_limit:
        return JsonResponse({'success': False, 'message': 'Your Ghost CMS integration limit has been reached.'}, status=200)

    # Validate request data
    try:
        ghost_admin_api_key = request.data["ghost_admin_api_key"]
        ghost_api_url = request.data["ghost_api_url"]
    except KeyError:
        return JsonResponse({'success': False, 'message': 'Missing required parameters.'}, status=400)

    # Check if this site is already integrated
    if user.ghost_integrations.filter(site_url=ghost_api_url).exists():
        return JsonResponse({'success': False, 'message': 'This site is already connected.'}, status=400)

    # Verify API Key with Ghost
    headers = {
        "Authorization": ghost_admin_api_key,
        "Content-Type": "application/json"
    }
    response = requests.get(f"{ghost_api_url}/ghost/api/admin/site/", headers=headers)

    if not response.ok:
        return JsonResponse({'success': False, 'message': 'Invalid Ghost Admin API Key or Site URL.'}, status=400)

    # Store integration in the database
    GhostIntegration.objects.create(
        website=user.current_active_website,
        api_key=ghost_admin_api_key,
        site_url=ghost_api_url,
    )

    return JsonResponse({'success': True, 'message': 'Ghost CMS integration successful.'})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def twya_submit(request: Request):
    """
    Submit Twya form data
    :param request: Django Rest Framework's Request object.
    """
    if not DEBUG:
        return JsonResponse404()

    user: User = request.user

    try:
        users_prompt = request.data['users_prompt']
        article_uid = request.data['article_uid']

        article: Article = user.articles.get(article_uid=article_uid)

        logger.info(f"Twya submit request for article: {article_uid}")
        logger.info(f"User's prompt: {users_prompt}")

        class ModifiedArticleContent(BaseModel):
            new_article_content_markdown: str

        llm = ChatOpenAI(
            model_name=get_gpt_model_name(),
            temperature=1,
            cache=True  # cache=True to enable caching
        )

        parser = PydanticOutputParser(pydantic_object=ModifiedArticleContent)
        prompt = PromptTemplate(
            template=modify_article_content_using_custom_prompt_text(),
            input_variables=["article_content", "instructions"],
            partial_variables={"format_instructions": parser.get_format_instructions()}
        )

        _input = prompt.format_prompt(
            article_content=article.content,
            instructions=users_prompt
        )

        _response = llm.invoke(_input.to_string())
        _output = _response.content

        total_tokens = _response.response_metadata["token_usage"]["total_tokens"]
        add_or_update_gpt_token_usage(total_tokens)

        structured_output: ModifiedArticleContent = parser.parse(_output)
        article.content = structured_output.new_article_content_markdown
        article.save()

        return JsonResponse(status=200, data={"success": True, "message": "Article content has been modified."})

    except KeyError as key:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_KEY", 'message': f"{key} is missing."})

    except Article.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ARTICLE", 'message': "No such article found."})

    except Exception as err:
        logger.critical(err)
        return JsonResponseBadRequest(additional_data={'err_id': "INTERNAL_SERVER_ERROR", 'message': "Internal server error."})


@api_view(["GET"])
@permission_classes([AllowAny])
def google_signup_login_auth(request: Request):
    """
    Returns authorization flow for google singup/login
    :param request: Django Rest Framework's Request object
    """
    try:
        signup: bool = request.GET["signup"]
    except KeyError as key:
        logger.error(f"Missing required key {key}")
        return JsonResponseKeyError()

    appsumo_code: str = request.GET.get('appsumo_code', None)

    try:
        # Using wordpress return url domain
        redirect_url = f"{WP_RETURN_URL_DOMAIN}/auth/accounts/google"

        if signup == "true":
            redirect_url += "/signup"
        else:
            redirect_url += "/login"

        # Create authorization flow
        flow = Flow.from_client_secrets_file(
            os.path.join(os.path.dirname(os.path.abspath(__file__)),
                         f'../google_client_secret/client_secret_singup_login.json'),
            scopes=[
                'https://www.googleapis.com/auth/userinfo.profile',
                'https://www.googleapis.com/auth/userinfo.email',
                'openid',
            ],
            redirect_uri=redirect_url
        )

        # Get the authorization url and state
        authorization_url, state = flow.authorization_url()

        # Add the state to redis task data db
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            redis_connection.set(state, 1)
            redis_connection.expire(state, 600)  # 10 minutes

        # Store appsumo code
        if appsumo_code:
            with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                redis_connection.set(f"{state}__appsumo_code", appsumo_code)
                redis_connection.expire(state, 600)  # 10 minutes

        return JsonResponse(
            status=200,
            data={
                'success': True,
                'authorization_endpoint': authorization_url
            }
        )

    except Exception as err:
        logger.critical(err)
        return JsonResponse(
            status=500,
            data={'success': False, 'authorization_endpoint': ""}
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_celery_task_progress(request: Request):
    user: User = request.user

    try:
        task_id: str = request.query_params['task_id']
    except KeyError:
        return JsonResponseBadRequest()

    try:
        task = AsyncResult(task_id)
        progress = Progress(task)

        if task.state == 'PROGRESS':
            return JsonResponse(status=200, data={'progress_info': progress.get_info()})
        elif task.state == 'SUCCESS':
            return JsonResponse(status=200, data={'progress_info': progress.get_info(), 'status': 'success'})
        elif task.state == 'FAILURE':
            return JsonResponse(status=500, data={'progress_info': progress.get_info(), 'status': 'failure'})
        else:
            return JsonResponse(status=200, data={'progress_info': progress.get_info(), 'status': task.state})

    except Exception as err:
        logger.exception(err)
        return JsonResponseBadRequest(additional_data={'err_id': "INTERNAL_SERVER_ERROR", 'message': "Internal server error."})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_backlinks(request: Request):
    """
    API to get backlinks list
    params: Django Request object
    return: list of backlinks
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        upgrade_plan = True
        backlinks = BackLink.objects.filter(show_on_free_plan=True).order_by('-show_on_free_plan')
        blured_backlinks = BackLink.objects.filter(show_on_free_plan=False).count()
    else:
        upgrade_plan = False
        backlinks = BackLink.objects.all().order_by('name')
        blured_backlinks = 0

    serializer = BackLinkSerializer(backlinks, many=True)
    return JsonResponse(status=200, data={"backlinks": serializer.data, 'upgrade': upgrade_plan, "blured_backlinks": blured_backlinks})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_webflow_sites(request: Request):
    """
    API to get webflow sites list
    params: Django Request object
    return: list of backlinks
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        logger.error("User is not allowed to use this API. User is on trial plan.")
        return JsonResponseBadRequest()

    serializer = WebflowSitesSerializer(user.webflow_integrations.all(), many=True)
    return JsonResponse(status=200, data={"table_data": serializer.data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_wordpress_sites(request: Request):
    """
    API to get wordpress sites list
    params: Django Request object
    return: list of backlinks
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        logger.error("User is not allowed to use this API. User is on trial plan.")
        return JsonResponseBadRequest()

    serializer = WordpressSitesSerializer(user.wordpress_integrations.all(), many=True)
    return JsonResponse(status=200, data={"table_data": serializer.data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_wix_sites(request: Request):
    """
    API to get wix sites list
    params: Django Request object
    return: list of backlinks
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        logger.error("User is not allowed to use this API. User is on trial plan.")
        return JsonResponseBadRequest()

    serializer = WixSitesSerializer(user.wix_integrations.all(), many=True)
    return JsonResponse(status=200, data={"table_data": serializer.data})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_ghost_sites(request: Request):
    """
    API to get ghost sites list
    params: Django Request object
    return: list of backlinks
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        logger.error("User is not allowed to use this API. User is on trial plan.")
        return JsonResponseBadRequest()

    serializer = GhostSitesSerializer(user.ghost_integrations.all(), many=True)
    return JsonResponse(status=200, data={"table_data": serializer.data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def remove_featured_image(request: Request):
    """
    API to remove featured image
    params: Django Request object
    return: success status
    """
    user: User = request.user

    try:
        article_uid: str = request.data['article_uid']
    except KeyError:
        return JsonResponseBadRequest()

    try:
        article: Article = user.articles.get(article_uid=article_uid)
    except Article.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ARTICLE"})

    if article.is_posted:
        return JsonResponseBadRequest(additional_data={'err_id': "ARTICLE_POSTED"})

    try:
        # delete all featured image for the article
        if article.selected_featured_image:
            # remove all featured images for the article
            article.selected_featured_image = None
            article.featured_images.clear()
            article.save()

    except Exception as err:
        logger.critical(err)
        return JsonResponseBadRequest(additional_data={'err_id': "FAILED_TO_REMOVE_FEATURED_IMAGE"})

    if article.selected_featured_image:
        return JsonResponseBadRequest(additional_data={'err_id': "FAILED_TO_REMOVE_FEATURED_IMAGE"})
    else:
        return JsonResponse(status=200, data={'success': True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_new_featured_image(request: Request):
    """
    API to generate new featured image
    params: Django Request object
    return: new featured image url
    """
    user: User = request.user

    try:
        article_uid: str = request.data['article_uid']
    except KeyError:
        return JsonResponseBadRequest()

    try:
        article: Article = user.articles.get(article_uid=article_uid)
    except Article.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ARTICLE"})

    if not user.feature_image_required:
        return JsonResponse(status=200, data={'title': article.title, 'featured_image': ""})

    # if article.is_posted:
    #     return JsonResponseBadRequest(additional_data={'err_id': "ARTICLE_POSTED"})

    if article.selected_featured_image:
        # get current featured images url
        current_template_image_url = article.selected_featured_image.template_image_url
    else:
        current_template_image_url = ""

    try:
        if "-text" in user.feature_image_template_id:
            template_photo_url = "https://res.cloudinary.com/diaiivikl/image/upload/v1709385716/ai-generated-image_rv1ice.jpg"
            ai_image_response: Dict = generate_AI_feature_image__sync(article_uid,
                                                                      user.feature_image_template_label == "premium" and "segmind" or "deepinfra")
            save_featured_image(ai_image_response["image_url"], "ai_image_generation",
                                article, user.feature_image_template_id, template_photo_url)

        else:
            # Prompt gpt to make the title shorter
            article_title: str = rephrase_article_title(article)

            # User selected template for featured image
            selected_template = user.feature_image_template_id

            # RE-Generate featured image from unsplash
            random_image_count = random.choice(range(1,10))
            template_photo_list = get_unsplash_images(article_title, random_image_count)
            template_photos_count = len(template_photo_list)

            # If images not found on unsplash then try to fetch it from pexels
            if not template_photos_count:
                template_photo_list = get_pexels_images(article_title, random_image_count)
                template_photos_count = len(template_photo_list)
            elif not template_photo_list[0]["url"]:
                template_photo_list = get_pexels_images(article_title, random_image_count)
                template_photos_count = len(template_photo_list)

            if template_photos_count >= 1:
                template_photo = template_photo_list[random.choice(range(template_photos_count-1))]["url"] if template_photos_count > 1 else template_photo_list[0]["url"]
            else:
                template_photo = ""

            if template_photo:
                # RE-Generate featured image
                own_image_response: Dict = generate_custom_feature_image(
                    template_photo,
                    user.current_active_website.logo_url if user.show_logo_on_featured_image else "",
                    article_title,
                    article_uid,
                    selected_template,
                    True,
                    user.images_file_format,
                    current_template_image_url
                )

                # Save featured image.
                if own_image_response["image_url"]:
                    save_featured_image(
                        own_image_response["image_url"],
                        "bannerbear",
                        article,
                        selected_template,
                        template_photo,
                    )

                else:
                    logger.error(f"Featured image generation failed for '{article_uid}' article")
                    return JsonResponseGone(additional_data={'err_id': "FEATURED_IMAGE_GENERATION_FAILED"})

    except Exception as err:
        logger.critical(err)
        return JsonResponseGone(additional_data={'err_id': "FEATURED_IMAGE_GENERATION_FAILED"})

    article.save()

    return JsonResponse(status=200, data={
        'title': article.title,
        'featured_image': article.selected_featured_image.image_url if article.selected_featured_image else None
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_featured_image(request: Request):
    """
    API to save manually uploaded featured image for the article
    params: articleUID, image file as base64
    return: new featured image url
    """
    user: User = request.user

    logger.info(f"User {user.email} is trying to upload a featured image for an article.")

    try:
        article_uid: str = request.data['article_uid']
        image_file = request.data['image_file']
    except KeyError:
        logger.error(f"Missing required key(s) for uploading featured image.")
        return JsonResponseBadRequest()

    try:
        article: Article = user.articles.get(article_uid=article_uid)
    except Article.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_SUCH_ARTICLE"})

    if not user.feature_image_required:
        return JsonResponseBadRequest(additional_data={'err_id': "FEATURE_IMAGE_IS_DISABLED"})

    if article.is_posted:
        return JsonResponseBadRequest(additional_data={'err_id': "ARTICLE_POSTED"})

    # delete existing featured image for the article
    if article.selected_featured_image:
        # remove all featured images for the article
        article.selected_featured_image = None
        article.featured_images.clear()
        article.save()

    try:
        # read the uploaded image file a base64 buffer
        image_content = base64.b64decode(image_file)

        # save the uploaded image
        image_url = download_and_save_image_bytes(image_content, article_uid)
        save_featured_image(image_url, "uploaded_image", article, "uploaded_image", image_url)
        article.save()

    except Exception as err:
        logger.critical(err)
        return JsonResponseBadRequest(additional_data={'err_id': "FAILED_TO_UPLOAD_FEATURED_IMAGE"})

    return JsonResponse(status=200, data={'title': article.title, 'featured_image': article.selected_featured_image.image_url if article.selected_featured_image else None})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_domain_logo(request: Request):
    """
    API to save manually uploaded company logo for the domain
    params: logo file as base64
    return: new domain logo url
    """
    user: User = request.user

    logger.info(f"User {user.email} is trying to upload a logo for domain.")

    try:
        image_file = request.FILES.get("image_file")
    except KeyError:
        logger.error(f"Missing required key(s) for uploading featured image.")
        return JsonResponseBadRequest()

    domain = user.current_active_website.domain if user.current_active_website is not None else None

    try:
        if Logo.objects.filter(domain=domain).exists():
            domain_logo = Logo.objects.get(domain=domain)
            domain_logo.image.delete()
            domain_logo.image = image_file
            domain_logo.save()
            user.current_active_website.logo_url = domain_logo.image.url
            user.current_active_website.save()
        else:
            domain_logo = Logo.objects.create(domain=domain, image=image_file)
            user.current_active_website.logo_url = domain_logo.image.url
            user.current_active_website.save()

    except Exception as err:
        logger.critical("Error during uploading domain logo err:", err)
        return JsonResponseBadRequest(additional_data={'err_id': "FAILED_TO_UPLOAD_DOMAIN_LOGO"})

    return JsonResponse(status=200, data={'domain_logo_url': domain_logo.image.url})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def post_survey(request: Request):
    """
    API to post survey
    params: Django Request object
    return: success message
    """
    user: User = request.user

    try:
        survey_data = json.loads(request.data['surveyData'])
    except KeyError as key:
        logger.error(f"post_survey() - Missing required key {key}")
        return JsonResponseBadRequest()

    try:
        survey = Survey(user=user, survey_data=survey_data)
        survey.save()
    except IntegrityError:
        logger.info(f"post_survey() - Survey already exists for user '{user.email}'. Updating survey data.")
        survey = Survey.objects.get(user=user)
        survey.survey_data = survey_data
        survey.save()

    # mark survey as completed
    user.survey_completed = True
    user.save()

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    return JsonResponse(status=200, data={
        "message": "OK",
        "success": True,
        "name": user.username,
        "user_id": user.id,
        "email": user.email,
        "signup_on": user.date_joined.strftime("%Y-%m-%d %H:%M:%S"),
        "plan_name": current_plan_name,
        "verified": user.verified,
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def fetch_serper_title_api(request: Request) -> Response:
    """
    API endpoint to fetch titles based on a keyword from the frontend.
    """
    user: User = request.user
    keyword = request.data.get('keyword')
    location = request.data.get('location', 'global')  # Default to 'global' if no location is provided
    max_titles = request.data.get('max_titles', 10)  # Default to 10 if not provided

    max_titles_allowed: int = get_stripe_product_data(user)['metadata']['max_titles']

    if (user.titles_generated + max_titles) > max_titles_allowed:
        return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_limit_reached', 'balance_title': max_titles_allowed})

    if not keyword:
        return JsonResponse({'success': False, 'error': 'Keyword is required.'}, status=400)

    # Get the current authenticated user
    user = request.user

    # Ensure the user is authenticated
    if not user.is_authenticated:
        return JsonResponse({'success': False, 'error': 'User is not authenticated.'}, status=401)

    # Create an instance of the ArticleTitleGeneratorV2 with the authenticated user
    title_generator = ArticleTitleGeneratorV2(user)


    try:
        titles = title_generator.fetch_titles_serper(keyword, location, max_titles)

        if not titles:
            return JsonResponse({'success': False, 'error': 'No titles generated.'}, status=404)

        user.titles_generated += len(titles)
        user.total_titles_generated += len(titles)
        user.save()
        return Response({'success': True, 'titles': titles})

    except Exception as e:
        logger.error(f"Error in fetch_titles_view: {e}")
        return Response({'success': False, 'error': str(e)}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def fetch_creative_title_api(request: Request):
    """
    Generate article titles based on a keyword and max titles.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: Generated article titles
    """
    user: User = request.user
    keyword = request.data.get('keyword')
    max_titles = int(request.data.get('max_titles', 8))  # Get max_titles, default to 8

    max_titles_allowed: int = get_stripe_product_data(user)['metadata']['max_titles']

    if (user.titles_generated + max_titles) > max_titles_allowed:
        return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_limit_reached', 'balance_title': max_titles_allowed})

    # Construct the prompt
    prompt_template = PromptTemplate(
        input_variables=["keyword", "max_titles"],
        template=(
            "Create {max_titles} simple & realistic article titles for keyword: {keyword}. "
            "The titles should be written in the same language as the input keyword. "
            "Include 'how to', listicle, etc type of article title. "
            "The titles should create curiosity within the user. "
            "Do not include any blog titles that have more than one adjective. "
            "Don't be verbose. "
            "Don't group article titles. "
            "Output should be in simple JSON format in an array."
        )
    )

    # Set up the OpenAI model
    llm = ChatOpenAI(
        model_name='gpt-4.1',
        temperature=0.8
    )

    # Create a RunnableSequence using the prompt directly
    chain = prompt_template | llm

    # Run the chain to generate titles
    result = chain.invoke({'keyword': keyword, 'max_titles': max_titles}).content

    # Clean up the result by stripping the unnecessary code block formatting
    cleaned_result = result.strip().replace("```json\n", "").replace("```", "").strip()

    # Attempt to parse the result as JSON
    data = json.loads(cleaned_result)
    
    user.titles_generated += len(data)
    user.total_titles_generated += len(data)
    user.save()

    return JsonResponse(status=200, data={'success': True, 'titles': data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def new_article_data_api(request: Request):
    """
    Generates article uid for new article.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: Generated article uid
    """
    user: User = request.user
    keyword_string: str = request.data.get('keyword')
    title: str = request.data.get('title')
    serp_titles: List = request.data.get('serp_titles')
    creative_titles: List = request.data.get('creative_titles')
    article_context: str = request.data.get('article_context')

    # convert article context to english if it is not in english
    if article_context:
        try:
            article_context = GoogleTranslator(source='auto', target='en').translate(article_context)
        except deep_translator.validate.NotValidLength as e:
            logger.error(f"Error in new_article_data_api: {e}")
            return JsonResponse({'success': False, 'error': 'Article context is too long.'}, status=400)

    # Check if the required fields are provided
    if not keyword_string or not title:
        return JsonResponse({'success': False, 'error': 'Keyword and title are required.'}, status=400)

    if len(keyword_string) > 300:
        return JsonResponse({'success': False, 'error': 'Keyword is too long.'}, status=400)

    # Normalize the keyword input
    keyword_string = unescape_amp_char(keyword_string.strip().lower())

    authenticated_user: User = request.user

    # Check if the user is verified
    if not authenticated_user.verified and not authenticated_user.has_ltd_plans:
        return JsonResponse({'success': False, 'error': 'User is not verified.'}, status=403)

     # Prepare keywords for batch processing
    keyword_list = [keyword_string]

    # Fetch keyword data in batches
    try:
        keyword_data_list = fetch_keyword_data(keyword_list)
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=500)
    
    
        # Process fetched keyword data
    for keyword_data in keyword_data_list:
        cleaned_keyword: str = re.sub(r'[^\w\s]', "", keyword_data['keyword'])
        keyword_object = create_keyword_object(
            user=authenticated_user,
            keyword=cleaned_keyword,
            with_default_values=False,  # Set to False since we have real data
            source=keyword_data['source'],  # This will be 'keywordseverywhere' or 'dataforseo'
            country=keyword_data['country'],  # This will be 'global'
            serp_position=keyword_data['serp_position'],  # This will be None
            volume=keyword_data['volume'],
            cpc_currency="USD",
            cpc_value=keyword_data['cpc'],
            paid_difficulty=keyword_data['paid_difficulty'],
            trend=keyword_data['trend'],
        )
        keyword_object.save()

    user.total_keywords_generated += 1
    user.save()
    keyword_project_id = str(uuid.uuid4())

    # Determine the project name based on the source of the title
    all_titles=[]

    if title in creative_titles:
        all_titles=creative_titles
        project_name = "Creative-KW-" + keyword_string[:6]

    elif title in serp_titles:
        all_titles=serp_titles
        project_name = "Serp-KW-" + keyword_string[:6]

    else:
        project_name = "AI-KW-" + keyword_string[:6]  # Fallback case if title source is unknown


    # Fetch the first KeywordProject for the user
    keyword_project= KeywordProject.objects.create(
        website=authenticated_user.current_active_website,
        project_id=keyword_project_id,
        project_name=project_name,
        location_iso_code='zz',
    )

    #Link the keyword to the KeywordProject using the reverse relationship
    keyword_project.keywords.add(keyword_object)

    # Update total traffic volume in the KeywordProject
    total_traffic_volume = sum(kw.volume for kw in keyword_project.keywords.all())
    keyword_project.total_traffic_volume = total_traffic_volume
    keyword_project.save()


    if title in all_titles:
        all_titles.remove(title)  # Remove the selected title from the list

    # Generate article for the selected title
    selected_article_uid = generate_article_uid(username=authenticated_user.username, article_type="default")

    # Create the selected article
    Article.objects.create(
        website=authenticated_user.current_active_website,
        article_uid=selected_article_uid,
        title=unescape_amp_char(title),
        keyword=keyword_object,
        context=article_context
    )

    # Generate articles for the remaining titles (without the selected title)
    article_bulk_ops = []
    for keyword_title in all_titles:
        article_uid = generate_article_uid(username=authenticated_user.username, article_type="default")
        article_bulk_ops.append(Article(
            website=authenticated_user.current_active_website,
            article_uid=article_uid,
            title=unescape_amp_char(keyword_title),
            keyword=keyword_object,
        ))

    Article.objects.bulk_create(article_bulk_ops)

    # Return the article_uid and keyword_project_id in the response
    return JsonResponse({
        'success': True,
        'article_uid': selected_article_uid,
        'message': 'ArticleUID created successfully.'
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def user_verified_api(request: Request):
    authenticated_user = request.user
    return JsonResponse({'success': True,'verified': authenticated_user.verified, 'message': 'OK'})


@api_view(['POST'])
def generate_titles_api_for_logout_users(request: Request):
    """
    Generate article titles based on a keyword for logout users.
    Type parameter determines whether to generate 'creative' or 'serp'-based titles.
    :param request: Django Rest Framework's Request object
    :return JsonResponse: Generated article titles
    """
    # Verify reCAPTCHA
    recaptcha_response = request.data.get('g-recaptcha-response')
    secret_key = os.environ['RECAPTCHA_SECRET_KEY']  # Set this in your environment variables


    recaptcha_verification_url = 'https://www.google.com/recaptcha/api/siteverify'
    payload = {
        'secret': secret_key,
        'response': recaptcha_response
    }

    recaptcha_response = requests.post(recaptcha_verification_url, data=payload)
    verification_result = recaptcha_response.json()
    logger.debug(verification_result)

    if not verification_result.get('success'):
        return JsonResponse({"error": "Invalid reCAPTCHA. Please try again."}, status=400)

    # Extract the necessary data from the request
    keyword = request.data.get('keyword')
    title_type = request.data.get('type','').lower()  # 'creative' or 'serp'
    max_titles = 10  # Default maximum number of titles

    if not keyword or not title_type:
        return JsonResponse({"error": "'keyword' and 'type' parameters are required."}, status=400)

    # For 'creative' type, generate creative titles using GPT
    if title_type == "creative":
        # Construct the prompt for generating creative titles
        prompt_template = PromptTemplate(
            input_variables=["keyword", "max_titles"],
            template=(
                "Create {max_titles} simple & realistic article titles for keyword: {keyword}. "
                "Include 'how to', listicle, etc. types of article titles. "
                "The titles should create curiosity within the user. "
                "Do not include any blog titles that have more than one adjective. "
                "Don't be verbose. Don't group article titles. "
                "Output should be in simple JSON format in an array."
            )
        )

        # Set up the OpenAI model
        llm = ChatOpenAI(
            model_name=get_gpt_model_name(),
            temperature=0.8
        )

        # Create an RunnableSequence and run it to generate creative titles
        chain = prompt_template | llm
        response = chain.invoke({'keyword': keyword, 'max_titles': max_titles})
        result = response.content

        total_tokens = response.response_metadata["token_usage"]["total_tokens"]
        add_or_update_gpt_token_usage(total_tokens)

        # Clean up the result
        cleaned_result = result.strip().replace("```json\n", "").replace("```", "").strip()

        try:
            data = json.loads(cleaned_result)
        except json.JSONDecodeError as e:
            return JsonResponse({"error": "Failed to parse generated titles.", "details": str(e)}, status=500)


        return JsonResponse(status=200, data={'success': True, 'titles': data})

    # For 'serp' type, generate titles based on Serper results
    elif title_type == "serp":
        # Call the function to generate titles from Serper API
        serper_articles = generate_titles_from_serper(keyword)

        if not serper_articles:
            return JsonResponse({"error": "No titles found from Serper."}, status=404)

        # Extract the titles
        example_title_sentences = '", "'.join([title['title'] for title in serper_articles])
        max_titles = len(serper_articles)

        # Construct the prompt for ChatOpenAI based on Serper results
        prompt_template = PromptTemplate(
            input_variables=["keyword", "example_title_sentences"],
            template=(
                "Generate {max_titles} long-tail blog titles for the keyword '{keyword}'.\n\n"
                "Example Titles:\n\"{example_title_sentences}\"\n\n"
                "Do permutation & combination with parts of the sentence to write new title sentences, but with slight changes. "
                "Avoid plagiarism, changing a word or two, but do not change anything related to the keyword '{keyword}'.\n"
                "Use 2024 as the year if a year is mentioned. Output should be in simple JSON array format."
            )
        )

        # Set up the OpenAI model
        llm = ChatOpenAI(
            model_name=get_gpt_model_name(),
            temperature=0.8
        )

        # Create an RunnableSequence and run it to generate new titles based on the Serper results
        chain = prompt_template | llm

        try:
            response = chain.invoke({
                'keyword': keyword,
                'example_title_sentences': example_title_sentences,
                'max_titles': max_titles
            })
            result = response.content

            total_tokens = response.response_metadata["token_usage"]["total_tokens"]
            add_or_update_gpt_token_usage(total_tokens)

            # Clean the result and parse it as JSON
            cleaned_result = result.strip().replace("```json\n", "").replace("```", "").strip()
            generated_titles = json.loads(cleaned_result)
            generated_titles = generated_titles[:max_titles]

        except Exception as e:
            return JsonResponse({"error": str(e)}, status=500)

        return JsonResponse({"success": True, "titles": generated_titles}, status=200)

    else:
        return JsonResponse({"error": "Invalid 'type' parameter. Must be 'creative' or 'serp'."}, status=400)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def get_article_progress(request: Request):
    article_uid = request.query_params.get('article_uid')

    if not article_uid:
        return JsonResponse({'status': 'error', 'message': 'article_uid is required'}, status=400)

    # Fetch the KubernetesJob with the specified article_uid and running status
    job = KubernetesJob.objects.filter(metadata=article_uid, status='running').first()

    if job:
        # Fetch logs for the Kubernetes job
        logs = fetch_k8s_logs(job.job_id)

        # Process logs to extract progress information
        progress_info = extract_progress_from_logs(logs)

        return JsonResponse({'status': 'success', 'progress': progress_info}, status=200)

    # Check if the article is in the queue
    article_generation_queue = ArticleGenerationQueue.objects.filter(article__article_uid=article_uid).order_by('created_at').first()

    if article_generation_queue:
        logger.info(f"Article generation task for {article_uid} is in queue.")

        if article_generation_queue.status == 'queued':
            return JsonResponse({'status': 'success', 'progress': {'percent': 0, 'description': 'In Queue...'}}, status=200)

        elif article_generation_queue.status == 'processing':
            return JsonResponse({'status': 'success', 'progress': {'percent': 0, 'description': 'Processing...'}}, status=200)

    # If the job is no longer running, assume it's completed
    return JsonResponse({
        'status': 'success',
        'progress': {
            'percent': 100,
            'description': 'Final Check!'
        }
    }, status=200)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_longtail_keyword_suggestions(request: Request):
    """
    API to get long-tail keyword suggestions
    params: Django Request object
    return: list of long-tail keyword suggestionsd
    """
    try:
        keyword: str = request.query_params["keyword"]
    except KeyError:
        return JsonResponseBadRequest()

    keywords: List[str] = get_longtail_keywords(keyword)
    return JsonResponse(status=200, data={"keywords": keywords})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def google_integration_fetch_data_api(request: Request):
    """
    API to fetch the data from integrated google APIs
    :param request: Django Rest Framework's Request object
    :return JsonResponse: Fetched data from google APIs
    """
    # Required data
    try:
        integration_type: str = request.data['integration_type']
        func_to_exec: str = request.data['execute']
    except KeyError as key:
        integration_type = 'google-search-console'
        func_to_exec = 'fetch_search_performance_analytics_data'

    # Optional data
    params: Dict = request.data.get('params', {})
    if not params and integration_type == 'google-search-console' and func_to_exec == 'fetch_search_performance_analytics_data':
        # last 30 days; start & end date should be in YYYY-MM-DD format
        params = {
            'start_date': (datetime.datetime.now() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'),
            'end_date': datetime.datetime.now().strftime('%Y-%m-%d')
        }
        logger.debug(f"google_integration_fetch_data_api: Fetching last 30 days keywords data from GSC.")

    if integration_type not in ['google-search-console', 'google-analytics', 'google-drive']:
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_INTEGRATION_TYPE",
                                                       'message': f"'{integration_type}' is not a valid integration type."})

    user: User = request.user
    website: Website = user.current_active_website

    integrated = getattr(website, f"{integration_type.replace('-', '_')}_integrated")

    if not integrated:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_INTEGRATED",
                                                       'message': f"'{integration_type}' is not integrated."})

    try:
        credentials: Credentials = google_integration_utils.get_google_oauth2_credentials(user, integration_type)
        site_url = google_integration_utils.get_site_name_on_gsc(credentials, website.domain)

        if not site_url:
            return JsonResponse404(additional_data={'err_id': "SITE_NOT_FOUND",
                                                    'message': f"Currently active site '{website.domain}' was not found on GSC."})

    except RefreshError:
        return JsonResponseBadRequest(additional_data={'err_id': "ACCESS_REVOKED_OR_EXPIRED_TOKEN",
                                                       'message': "Access has been revoked by the user or the token has expired."})

    try:
        callable_func: Callable = getattr(google_integration_utils, func_to_exec)
        params.update({
            'credentials': credentials,
            'website_url': site_url
        })

        data = callable_func(**params)
        return JsonResponse(status=200, data=data)

    except AttributeError:
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_FUNCTION",
                                                       'message': f"'{func_to_exec}' is not a valid function to execute."})

    except TypeError as err:
        return JsonResponseBadRequest(additional_data={'err_id': "INVALID_PARAMS", 'message': str(err)})

    except HttpError as err:
        logger.error(err)

        try:
            status_code = err.resp.status
        except AttributeError:
            status_code = 500

        try:
            message = err.reason
        except AttributeError:
            message = "Failed to fetch the data from google"

        return JsonResponse(status=status_code, data={'err_id': "GOOGLE_API_ERROR", 'message': message})

    except Exception as err:
        logger.critical(err)
        return JsonResponseServerError()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def send_pages_for_indexing(request: Request):
    """
    Send pages for indexing

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website
    max_pages_allowed: int = 200

    try:
        pages_to_send_for_indexing: str = request.data['all_pages_urls']
    except KeyError:
        return JsonResponseBadRequest()

    if not website.google_search_console_integrated:
        logger.error(f"User {user.email} does not have an GSC integration set up")
        return JsonResponseBadRequest()

    credentials: Credentials = google_integration_utils.get_google_oauth2_credentials(user, "google-search-console")    
    website_indexation = WebsiteIndexation.objects.get(user=user, website=website)    
    indexation_details: List[Dict] = website_indexation.indexation_details

    if website_indexation.urls_sent_for_indexing >= max_pages_allowed:
        return JsonResponse(status=200, data={'success': False, 'message': f"Google allows indexing of only {max_pages_allowed} pages per day."})

    all_pages = [page['url'] for page in indexation_details if page.get('indexed') is not None]
    # logger.info(f"all_pages {all_pages}")
    total_pages_sent_for_indexing = 0

    for page_url in pages_to_send_for_indexing:
        if page_url in all_pages:
            article_sent_for_indexing, error_reason = index_url(page_url, credentials)
            logger.info(f"article_sent_for_indexing {article_sent_for_indexing}")
            if article_sent_for_indexing:
                indexation_details[all_pages.index(page_url)]['sent_for_indexing'] = True
                indexation_details[all_pages.index(page_url)]['status'] = "Request Sent for Indexing"
                indexation_details[all_pages.index(page_url)]['sent_on'] = datetime.datetime.now(tz=ZoneInfo('UTC')).isoformat()
                total_pages_sent_for_indexing += 1
            else:
                if "insufficient authentication scopes" in error_reason:
                    return JsonResponse(status=200, data={
                        'message': "Your integration does not have the necessary permissions to retrieve the required data. " \
                                   "Reconnect your Google Search Console account integration and ensure that you grant all required permissions.",
                        'success': False
                    })
                else:
                    logger.error(f"Failed to send the '{page_url}' for indexing")

        # Check MAX limit
        if total_pages_sent_for_indexing >= max_pages_allowed:
            break

    # save updated indexation details
    website_indexation.indexation_details = indexation_details
    website_indexation.urls_sent_for_indexing += total_pages_sent_for_indexing
    website_indexation.save()

    return JsonResponse(status=200, data={'total_pages': total_pages_sent_for_indexing, 'success': True})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def fetch_all_pages(request: Request):
    """
    Fetch all the pages from connected website sitemap

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        website_indexation = WebsiteIndexation.objects.get(user=user, website=website)
    except WebsiteIndexation.DoesNotExist:
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})

    # indexation_details: List[Dict] = website_indexation.indexation_details
    indexation_details: List[Dict] = website_indexation.indexation_details
        
    if not indexation_details:
        return JsonResponse(status=200, data={'all_pages': all_pages, 'message': "OK"})
    
    all_pages = []

        
    for page in indexation_details:
        if page.get('indexed') is not None:
            index = (
                True if page.get('indexed', False) or page.get("status", "") == "Index Completed"
                else False if not page.get('indexed', False) and not page.get('sent_for_indexing', False)
                else False  
            )
            all_pages.append({
                'index': index,
                'url': page['url'],
                'last_crawled': page.get('last_crawled', ""),
                'in_progress': page.get('sent_for_indexing', False) and page.get("status", "") not in ["Index Completed", "Rejected"],
                'status': page.get('status', '')
            })
    
    return JsonResponse(status=200, data={'all_pages': all_pages, 'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def fetch_pages_sent_for_indexing(request: Request):
    """
    Fetch pages sent for indexing

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        website_indexation = WebsiteIndexation.objects.get(user=user, website=website)
    except WebsiteIndexation.DoesNotExist:
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})    
    
    indexation_details: List[Dict] = website_indexation.indexation_details
    all_pages = []

    if not indexation_details:
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})

    for page in indexation_details:
        if page.get('sent_for_indexing') and page.get('status') == "Request Sent for Indexing":            
            
            all_pages.append({
                'url': page['url'],
                'date': datetime.datetime.fromisoformat(page['sent_on']).astimezone(tz=ZoneInfo(user.user_tz)).strftime("%d %B, %Y, %H:%M %Z"),
                'status': page.get('status') or "Request Sent for Indexing",
                # 'indexed_on': page.get('last_crawled')
            })

    return JsonResponse(status=200, data={'all_pages': all_pages, 'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def fetch_all_indexed_pages(request: Request):
    """
    Fetch all posted abun articles

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        website_indexation = WebsiteIndexation.objects.get(user=user, website=website)
    except WebsiteIndexation.DoesNotExist:        
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})

    indexation_details: List[Dict] = website_indexation.indexation_details
        
    if not indexation_details:
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})
    
    # if not indexation_details:
    #     fetched_pages = []
    # else:
    #     fetched_pages = [page['url'] for page in indexation_details]

    # posted_articles_urls = Article.objects.filter(website__user=user, is_posted=True).values_list('article_link', flat=True)
    all_pages = []


    for page in indexation_details:
        if page.get('indexed') is not None:
            index = (
                True if page.get('indexed', False) or page.get("status", "") == "Index Completed"
                else False if not page.get('indexed', False) and not page.get('sent_for_indexing', False)
                else False
            )
            if index: 
                all_pages.append({
                    'index': index,
                    'url': page['url'],
                    'last_crawled': page.get('last_crawled', ""),                  
                })
                
    return JsonResponse(status=200, data={'all_pages': all_pages, 'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def fetch_all_not_indexed(request: Request):
    """
    Fetch all posted abun articles

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        website_indexation = WebsiteIndexation.objects.get(user=user, website=website)
    except WebsiteIndexation.DoesNotExist:        
            return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})

    indexation_details: List[Dict] = website_indexation.indexation_details
        
    if not indexation_details:
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})
    
    all_pages = []

    for page in indexation_details:
        if page.get('indexed') is not None:
            index = (
                False if not page['indexed'] and not page.get('sent_for_indexing') else True
            )
            if not index and not (page.get('sent_for_indexing', False) and page.get("status", "") != "Index Completed") and page.get("status", "") != "Rejected": 
                all_pages.append({
                    'index': index,
                    'url': page['url'],
                    'last_crawled': page.get('last_crawled', ""),
                    'status': page.get('status'),
                    'coverage': page.get('coverage_state'),
                    'sent_for_indexing' :  page.get('sent_for_indexing', False)
                })
                
    return JsonResponse(status=200, data={'all_pages': all_pages, 'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def indexation_report_stats(request: Request):
    """
    Generate indexation report stats

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        website_indexation = WebsiteIndexation.objects.get(user=user, website=website)
    except WebsiteIndexation.DoesNotExist:
        # website_indexation = indexation_page_url(user, website)
        # if not website_indexation:
            return JsonResponse(status=200, data={'index_stats_report': [], 'message': "OK"})

    indexation_details: List[Dict] = website_indexation.indexation_details
    logger.info(f"indexation_details {indexation_details}")
    total_pages = 0
    pages_index = 0
    page_not_indexed  = 0
    pages_sent_for_indexing = 0
    pages_rejected = 0
    # total_abun_articles = 0
    # abun_articles_indexed = 0
    # abun_articles_sent_for_indexing = 0

    if indexation_details:
        # posted_articles: QuerySet = Article.objects.filter(website__user=user, is_posted=True).values_list('article_link', flat=True)
        # total_abun_articles = posted_articles.count()

        for page in indexation_details:
            if page.get('indexed') is None:
                continue

            total_pages += 1

            if page['indexed'] or page.get("status") == "Index Completed":
                pages_index += 1
            
            if not page['indexed'] and not page.get('sent_for_indexing') and page.get("status", "") != "Rejected":
                page_not_indexed += 1

            if page.get('sent_for_indexing') and page.get("status") == "Request Sent for Indexing":
                pages_sent_for_indexing += 1
            
            if page.get("status", "") == "Rejected":
                pages_rejected += 1
            
    stats_report = [
        # {
        #     'id': 1,
        #     'title': "Total Pages",
        #     'count': total_pages,
        #     'icon_name': "paragraph"
        {
            'id': 2,
            'title': "Total Pages Indexed",
            'count': pages_index,
            'icon_name': "lighting"
        },
        {
            'id': 3,
            'title': "In Progress",
            'count': pages_sent_for_indexing,
            'icon_name': "green-checkmark-circle"
        },
        {
            'id': 4,
            'title': "Rejected",
            'count': pages_rejected,
            'icon_name': "paragraph"
        },
        # {
        #     'id': 5,
        #     'title': "Abun Articles Indexed",
        #     'count': abun_articles_indexed,
        #     'icon_name': "lighting"
        # },
        {
            'id': 6,
            'title': "Not Indexed",
            'count': page_not_indexed,
            'icon_name': "paragraph"
        },
    ]

    return JsonResponse(status=200, data={'index_stats_report': stats_report, 'message': "OK"})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def fetch_all_rejected_page(request: Request):
    user: User = request.user
    website: Website = user.current_active_website

    try:
        website_indexation = WebsiteIndexation.objects.get(user=user, website=website)
    except WebsiteIndexation.DoesNotExist:      
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})

    indexation_details: List[Dict] = website_indexation.indexation_details
        
    if not indexation_details:
        return JsonResponse(status=200, data={'all_pages': [], 'message': "OK"})
    
    all_pages = []


    for page in indexation_details:
        if page.get('indexed') is not None:           
            if page.get('status', "") == "Rejected" : 
                all_pages.append({                    
                    'url': page['url'],
                    'status': page.get('status', ''),
                    'reason': page.get('coverage_state', ''),                  
                })
                

    return JsonResponse(status=200, data={'all_pages': all_pages, 'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_keyword_projects(request: Request):
    """
    Authenticates & returns paginated data for keywords research page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    # Get pagination parameters
    page = int(request.GET.get('page', 1))
    per_page = int(request.GET.get('per_page', 10))
    search = request.GET.get('search', '')
    sort = request.GET.get('sort', '')

    # Get the most recent article timestamp for each project
    latest_article_timestamp_subquery = user.articles.filter(
        keyword__in=KeywordProject.keywords.through.objects.filter(
            keywordproject_id=OuterRef('pk')
        ).values('keyword_id')
    ).order_by('-created_on').values('created_on')[:1]

    # Base queryset with annotations
    queryset = (
        user.keyword_projects
        .exclude(project_name__startswith="programmatic-seo")  # Exclude projects with "programmatic-seo"
        .select_related()
        .prefetch_related('keywords')
        .annotate(
            keyword_count=Count('keywords'),
            most_recent_art_title_timestamp=Subquery(latest_article_timestamp_subquery)
        )
    )

    # Get total count before pagination
    total_count = queryset.count()

    # Apply search if provided
    if search:
        queryset = queryset.filter(
            Q(project_name__icontains=search) |
            Q(location_iso_code__icontains=search)
        )

    # Apply sorting if provided
    if sort:
        sort_fields = []
        for sort_item in sort.split(','):
            field, direction = sort_item.split(':')
            # Map frontend field names to database field names
            field_mapping = {
                'projectName': 'project_name',
                'totalKeywords': 'keyword_count',
                'totalTrafficVolume': 'total_traffic_volume',
                'dateCreated': 'created_on',
                'mostRecentArtTitleTimestamp': 'most_recent_art_title_timestamp'
            }
            db_field = field_mapping.get(field, field)
            if direction == 'desc':
                db_field = f'-{db_field}'
            sort_fields.append(db_field)

        if sort_fields:
            queryset = queryset.order_by(*sort_fields)

    else:
        # Default sorting
        queryset = queryset.order_by('-created_on')

    if per_page == -1:
        # Fetch all data
        page_obj = queryset
    else:
        # Apply pagination
        paginator = Paginator(queryset, per_page)
        page_obj = paginator.get_page(page)

    # Prepare response data
    response_data = [
        {
            'projectName': unescape_amp_char(project.project_name),
            'totalKeywords': project.keyword_count,
            'totalTrafficVolume': project.total_traffic_volume,
            'dateCreated': project.created_on.timestamp(),
            'projectId': str(project.project_id),
            'locationIsoCode': project.location_iso_code,
            'mostRecentArtTitleTimestamp': project.most_recent_art_title_timestamp.timestamp() if project.most_recent_art_title_timestamp else None,
        }
        for project in page_obj
    ]

    return JsonResponse(
        status=200,
        data={
            "keyword_projects": response_data,
            "total": total_count,
        },
        safe=False,
    )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_keyword_project(request: Request):
    """
    Authenticates & returns data for selected keyword project.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    project_id = request.query_params.get('project_id')

    if not project_id:
        return JsonResponse({'error': 'Project ID is required'}, status=400)

    try:
        # Prefetch related data to optimize the queries
        keyword_project = user.keyword_projects.prefetch_related(
            'keywords__article_set'
        ).get(project_id=project_id)
    except KeywordProject.DoesNotExist:
        return JsonResponse({'message': 'KeywordProject not found'}, status=404)

    # Prepare response data
    response_data = {
        'projectName': unescape_amp_char(keyword_project.project_name),
        'totalKeywords': keyword_project.keywords.count(),
        'totalTrafficVolume': keyword_project.total_traffic_volume,
        'dateCreated': keyword_project.created_on.timestamp(),
        'projectId': str(keyword_project.project_id),
        'locationIsoCode': keyword_project.location_iso_code,
    }

    return JsonResponse(
        status=200,
        data={
            "keyword_project": response_data,
        },
        safe=False,
    )


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_automation_projects(request: Request):
    """
    Authenticates & returns page data for automation page.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['display_name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if 'Trial' in current_plan_name:
        return JsonResponseBadRequest(additional_data={'err_id': "NOT_ALLOWED", 'message': "API is not allowed on Trial Plan."})

    # Get pagination parameters
    page = int(request.GET.get('page', 1))
    per_page = int(request.GET.get('per_page', 10))
    search = request.GET.get('search', '')

    try:
        automation_projects = user.automation_projects.select_related(
            'associated_keyword_project'
        ).order_by('-created_on')

        if not automation_projects.exists():
            return JsonResponse(status=200, data={
                'automation_projects': [],
                'total': 0,
            })

        # Apply search if provided
        if search:
            automation_projects = automation_projects.filter(
                Q(project_name__icontains=search) |
                Q(associated_keyword_project__project_name__icontains=search) |
                Q(associated_keyword_project__keyword__keyword__icontains=search) |
                Q(selected_integration_name__icontains=search)
            )

        # Get total count before pagination
        total_count = automation_projects.count()

        # Apply pagination
        start = (page - 1) * per_page
        end = start + per_page
        paginated_queryset = automation_projects[start:end]

        return JsonResponse(
            status=200,
            data={
                'automation_projects': AutomationProjectSerializer(paginated_queryset, many=True).data,
                'total': total_count,
            }
        )

    except Exception as e:
        logger.error(f"get_automation_projects() - {e}")
        return JsonResponse({"error": "Internal Server Error"}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def programmatic_seo_titles(request: Request):

    """
    This feature will allow users to automatically generate SEO-optimized titles in bulk using a specified pattern and input examples.
    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    website: Website = user.current_active_website
    
    no_of_titles = request.data.get('n_titles')
    pattern = request.data.get('pattern')
    examples = unescape_amp_char(request.data.get('example_pattern'))
    selected_location = request.data['selectedLocation']

     # Validation
    if not isinstance(no_of_titles, int) or no_of_titles <= 0:
        return JsonResponse(data={'error': "n_titles should be a positive integer"}, status=400)

    if not isinstance(pattern, str) or '{' not in pattern or '}' not in pattern:
        return JsonResponse(data={'error': "pattern must be a valid string containing placeholders like {placeholder}"}, status=400)

    if not isinstance(examples, list) or len(examples) < 3:
        return JsonResponse(data={'error': "example_pattern should be a list containing at least 3 examples"}, status=400)

     # Check if examples contain blocked keywords
    block_keywords = BlockKeywords.objects.all().values_list('keyword', flat=True)
    for keyword in examples:
        if any(block_kw in keyword for block_kw in block_keywords):
            return JsonResponse(status=200, data={
                "status": "rejected",
                "reason": "blocked_keyword_used",
                "message": f"You cannot add '{keyword}' keyword. If you feel this is incorrect, please contact us via live chat."
            })

    max_titles_allowed: int = get_stripe_product_data(user)['metadata']['max_titles']

    if (user.titles_generated + no_of_titles) > max_titles_allowed:
        return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_limit_reached', 'balance_title': max_titles_allowed})

    project_name = "programmatic-seo-" + examples[0][:15]

    try:
        # Create a string representation of the examples
        example_text = "\n".join(examples)
        
        try:
            keywords_data_from_keywords_everywhere = get_keyword_volume_data(examples, selected_location['country_iso_code'].lower())            
        except Exception as err:
            logger.critical(err)    
            return JsonResponse(status=400, data={"status": "error", "reason": "keyword_volume_fetch_failed", "message": "Failed to fetch keyword volume data."})
        new_keywords_with_data = []
        for idx, keyword_data in enumerate(keywords_data_from_keywords_everywhere):
            cleaned_keyword = re.sub(r'[^a-zA-Z0-9\s]', "", keyword_data['keyword'])

            if not keyword_data["vol"]:
                keyword_obj = create_keyword_object(user, cleaned_keyword, with_default_values=True, country=selected_location['country_iso_code'], source="user-keyword")
                new_keywords_with_data.append(keyword_obj)
                continue

            keyword_obj = create_keyword_object(user, cleaned_keyword, **{
                "source": "keywordseverywhere",
                "country": selected_location['country_iso_code'],
                "serp_position": None,
                "volume": keyword_data["vol"],
                "cpc_currency": keyword_data['cpc']['currency'],
                "cpc_value": keyword_data['cpc']['value'],
                "paid_difficulty": keyword_data["competition"],
                "trend": keyword_data["trend"]
            })
            new_keywords_with_data.append(keyword_obj)        

        if not new_keywords_with_data:
            return JsonResponse(data={"status": "rejected", "reason": "no_keywords_found", "message": "No keywords found. Please try with different keywords."})

        try:
            keyword_project = KeywordProject.objects.create(
                website=website,
                project_name=project_name,
                project_id=str(uuid.uuid4())[:16],
                location_iso_code=selected_location['country_iso_code'].lower(),
            )            
        except Exception as err:
            logger.critical(err)      
            return JsonResponse(status=400, data={"status": "error", "reason": "keyword_project_creation_failed", "message": "Failed to create keyword project."})

        try:
            with transaction.atomic(using='default'):
                Keyword.objects.bulk_create(new_keywords_with_data, ignore_conflicts=True)                    
        except Exception as err:
            logger.critical(err)
            return JsonResponse(status=400, data={"status": "error", "reason": "keyword_save_failed", "message": "Failed to save keywords."})

        try:
            with transaction.atomic(using='default'):
                keyword_project.keywords.add(*new_keywords_with_data)

            keyword_project.total_traffic_volume = sum([kw.volume for kw in new_keywords_with_data])
            keyword_project.save()            
        except Exception as err:     
            logger.critical(err)   
            return JsonResponse(status=400, data={"status": "error", "reason": "keyword_association_failed", "message": "Failed to associate keywords with project."})
        
        seo_title = ProgrammaticSeoTitle.objects.create(
                website=website,
                keyword_project=keyword_project,
                pattern=unescape_amp_char(pattern.strip()),
                is_processing=True               
            )
        try: 
            task = celery_pseo_title_gen.delay(user.id, no_of_titles, pattern, example_text, new_keywords_with_data[0].keyword_md5_hash, seo_title.id)                
        except:
            seo_title.is_processing = False
            seo_title.save()
            return JsonResponse(status=400, data={"status": "success", "message": "Failed to create task."})    
        return JsonResponse(status=202, data={"status": "success", "message": "Task has been queued.", "task_id": task.id, "keyword_hash": new_keywords_with_data[0].keyword_md5_hash, "project_id": keyword_project.project_id, "pattern": pattern})
        
    except Exception as e:
        logger.error(e)
        return JsonResponse(data={'error': f"An error occurred: {str(e)}"}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_seo_projects(request: Request):
    """
    Retrieve all objects from ProgrammaticSeoTitle model and return keyword projects and titles.
    """
    user: User = request.user
    current_plan_data: Dict = get_stripe_product_data(user)
    
    try:
        seo_projects = user.programmatic_seo_titles.order_by(
            '-created_at'  # Newest first
        ).prefetch_related(
            Prefetch('articles', queryset=Article.objects.select_related("keyword"))
        )

        seo_projects_list = []
        for project in seo_projects:
            articles = project.articles.all()
            count = articles.count()

            if count == 0 and not project.is_processing:
                continue

            keyword = None
            if count > 0:
                first_article = articles[0]
                keyword = getattr(first_article, 'keyword', None)

            total_article_generated = articles.filter(is_generated=True).count()

            seo_projects_list.append({
                "keyword_project": unescape_amp_char(project.keyword_project.project_name),
                "project_id": project.keyword_project.project_id,
                "total_traffic_volume": project.keyword_project.total_traffic_volume,
                "location_iso_code": project.keyword_project.location_iso_code,
                'keywordHash': keyword.keyword_md5_hash if keyword else None,
                'keywordTraffic': keyword.volume if keyword else None,
                'difficultyScore': keyword.paid_difficulty if keyword else None,
                'isProcessing': project.is_processing,
                'total_article_generated': total_article_generated,
                "count": count,
            })

        return JsonResponse({'status': 'success', 'data': seo_projects_list}, status=200)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def shopify_integration(request: Request):
    """
    Connect shopify shop api

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    # do not proceed if user is not on a paid plan
    try:
        current_plan = get_stripe_product_data(user)
        current_plan_name = current_plan['name']
    except stripe.error.InvalidRequestError:
        current_plan = None
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    shopify_website_limit = current_plan['metadata']['websites']

    if user.shopify_integrations.count() >= shopify_website_limit:
        return JsonResponse(status=200, data={
            "success": False,
            "message": "Your Shopify sites limit has been reached. Please upgrade your plan to increase the limit."
        })


    try:
        shop_url = request.data["shop_url"]
        access_token = request.data["access_token"]
    except KeyError:
        return JsonResponseKeyError()

    try:
        shopify_integration = user.shopify_integrations.filter(shop_url=shop_url)

        if shopify_integration.exists():
            return JsonResponse(status=200, data={'success': False, 'message': "This shop is already connected to your Abun account."})

        if shop_url and access_token:
            # Verify Shopify credentials by calling the Shopify API
            if shop_url.startswith("http"):
                shop_url = shop_url.replace("http://", "").replace("https://", "")

            if shop_url.endswith("/"):
                shop_url = shop_url[:-1]

            api_url = f"https://{shop_url}/admin/api/{SHOPIFY_API_VERSION}/shop.json"
            headers = {"X-Shopify-Access-Token": access_token}

            try:
                response = requests.get(api_url, headers=headers, timeout=120)
            except requests.exceptions.Timeout:
                return JsonResponse(status=200, data={"success":False, "message": "Failed to connect with your Shopify store."})

            if not response.ok:
                return JsonResponse(status=200, data={"success":False, "message": "Invalid shopify credentials."})

            add_shopify_integration(user, shop_url, access_token)
            return JsonResponse(status=200, data={'success': True, 'message': "OK"})

        else:
            return JsonResponse(status=200, data={'success': False, 'message': "shop_url and access_token are required."})

    except Exception as err:
        logger.critical(f"Error in shopify_integration_auth() api : {err}")
        return JsonResponse(status=500, data={"success": False, "message": f"Internal Server Error."})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_shopify_shops(request: Request):
    """
    API to get shopify shops list
    params: Django Request object
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        logger.error("User is not allowed to use this API. User is on trial plan.")
        return JsonResponseBadRequest()

    serializer = ShopifyShopsSerializer(user.shopify_integrations.all(), many=True)
    return JsonResponse(status=200, data={"table_data": serializer.data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_appsumo_license_to_user_account(request: Request):
    """
    API to activate & add AppSumo license to User account
    params: Django Request object
    """
    user: User = request.user

    try:
        appsumo_code = request.data['appsumo_code']
    except KeyError:
        return JsonResponseBadRequest({'err_id': "MISSING_REQUIRED_KEY", 'message': "Missing required key 'appsumo_code'."})

    json_response = fetch_appsumo_access_token(appsumo_code)

    if not json_response:
        logger.error("Failed to fetch the access token from appsumo.")
        return JsonResponseBadRequest({'err_id': "FAILED_TO_FETCH_ACCESS_TOKEN"})

    else:
        access_token = json_response['access_token']
        refresh_token = json_response['refresh_token']
        license_key = fetch_appsumo_user_license_key(access_token, refresh_token)

        try:
            appsumo_license = AppSumoLicense.objects.get(license_key=license_key)

            if appsumo_license.user:
                logger.error(f"{user.email} -> '{appsumo_license.license_key}' is already in use.")
                return JsonResponseBadRequest({'err_id': "ALREADY_IN_USE"})

            # Add license to user account
            user.appsumo_licenses.add(appsumo_license)

        except AppSumoLicense.DoesNotExist:
            logger.error(f"{user.email} -> No AppSumo license Found with '{license_key}'.")
            return JsonResponseBadRequest({'err_id': "LICENSE_KEY_NOT_FOUND"})

        except Exception as err:
            logger.critical(err)
            return JsonResponseServerError()

    return JsonResponse(status=200, data={"success": True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def cancel_user_subscription(request: Request):
    """
    API to cancel user subscription
    params: Django Request object
    """
    user: User = request.user

    try:
        subscription_id = request.data['sub_id']
    except KeyError:
        return JsonResponseBadRequest({'err_id': "MISSING_REQUIRED_KEY", 'message': "Missing required key 'sub_id'."})

    # Mark the AppSumo LTD plan as cancel
    if subscription_id.startswith("appsumo-ltd-id-"):
        try:
            appsumo_obj_id = int(subscription_id.replace("appsumo-ltd-id-", ""))
        except ValueError:
            return JsonResponseBadRequest({'err_id': "SUBSCRIPTION_ID_NOT_FOUND"})

        try:
            appsumo_license = AppSumoLicense.objects.get(id=appsumo_obj_id)
        except AppSumoLicense.DoesNotExist:
            logger.error(f"No appsumoe license found with '{appsumo_obj_id}' ID.")
            return JsonResponseBadRequest({'err_id': "SUBSCRIPTION_ID_NOT_FOUND"})

        if user.email != appsumo_license.user.email:
            logger.error(f"{user.email} user does not have this subscription plan in his account.")
            return JsonResponseBadRequest({'err_id': "MISMATCH_SUBSCRIPTION"})

        appsumo_license.license_status = "cancel"
        appsumo_license.save()

        return JsonResponse(status=200, data={"success": True})

    # Cancel subscription plan on stripe
    # NOTE: Upon receiving the "customer.subscription.deleted" webhook event from Stripe, we will transition the user to the free plan.
    if user.stripe_subscription_id != subscription_id:
        logger.error(f"{user.email} user does not have this subscription plan in his account.")
        return JsonResponseBadRequest({'err_id': "MISMATCH_SUBSCRIPTION"})

    try:
        # Cancel the subscription
        stripe.Subscription.delete(subscription_id)
        return JsonResponse(status=200, data={"success": True})

    except stripe.error.InvalidRequestError as err:
        logger.error("Failed to cancel the User subscription")
        logger.critical(err)

    except Exception as err:
        logger.critical(err)

    return JsonResponseServerError()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_sitemap(request: Request):
    """
    API to add the Sitemap to the user's currently active website
    params: Django Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_CONNECTED"})

    try:
        sitemap_url = request.data['sitemap_url']
    except KeyError:
        return JsonResponseKeyError()

    sitemap_domain = tldextract.extract(sitemap_url)

    # Check the sitemap domain
    if not sitemap_domain or f"{sitemap_domain.domain}.{sitemap_domain.suffix}" != website.domain:
        return JsonResponse(status=200, data={
            'success': False,
            'err_id': "INVALID_SITEMAP_DOMAIN",
            'message': "Sitemap domain does not match with the connected website domain."
        })

    # Make a get request to the URl to check the response
    try:
        response = requests.get(sitemap_url)

    except requests.exceptions.SSLError as err:
        logger.error(err)
        return JsonResponse(status=200, data={
            'success': False,
            'err_id': "SSL_ERROR",
            'message': "Failed to verify your SSL certificate. Please try again after sometime."
        })

    except requests.exceptions.ConnectionError as err:
        logger.error(err)
        return JsonResponse(status=200, data={
            'success': False,
            'err_id': "CONNECTION_ERROR",
            'message': "Failed to establish connection with your Website. Please try again after sometime."
        })

    if not response.ok:
        return JsonResponse(status=200, data={
            'success': False,
            'err_id': "BAD_STATUS_CODE",
            'message': f"Failed to fetch links from sitemap due to an unexpected status code. Status Code: {response.status_code}"
        })

    if not website.pages_scan_limit_reached:
        # Delegate task to celery
        celery_start_website_scanning.delay(website.domain, sitemap_url, run='generate-summary')

    return JsonResponse(status=200, data={'message': "OK", 'success': True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_glossary_topic(request: Request):
    """
    Generate glossary topics using an LLM.
    """
    user: User = request.user
    website: Website = user.current_active_website
    word = unescape_amp_char(request.data.get('word'))
    no_of_words = 100

    # Validation
    if not isinstance(word, str) or not word.strip():
        return JsonResponse(data={'error': "word must be a valid non-empty string"}, status=400)

    # Check if the month has changed and reset glossary_topic_generated
    current_month = timezone.now().month
    last_reset_month = user.last_glossary_reset.month

    if current_month != last_reset_month:
        # Reset glossary_topic_generated and update last_glossary_reset
        user.glossary_topic_generated = 0
        user.glossary_contents_generated = 0
        user.last_glossary_reset = timezone.now()
        user.save()

    # Check user limits
    product_data = get_stripe_product_data(user)
    max_glossary_topics_allowed = product_data['metadata']['topics']  # Maximum topics allowed

    if (user.glossary_topic_generated) >= max_glossary_topics_allowed:
        return JsonResponse(
            status=200,
            data={
                'status': "rejected",
                'reason': 'max_limit_reached',
                'balance_title': max_glossary_topics_allowed,
            }
        )

    try:
        # Generate glossary words using the provided input
        project_name = f"glossary-topic-{word[:15]}"
        project_id = str(uuid.uuid4())[:16]
        
        glossary_topic = GlossaryTopic.objects.create(
            website=website,
            project_id=project_id,
            word=word.strip(),
            glossary_words=[]
        )
        
        task = celery_generate_glossary_words.delay(user.id, project_id, word, no_of_words)

        return JsonResponse(
            status=200,
            data={
                "message": "Generating Glossary topics",
                "word": glossary_topic.word,
                "project_id": project_id,
                "project_name": project_name,
                "task_id": task.id
            },
        )

    except Exception as e:
        logger.error(f"Error generating glossary topics: {str(e)}")
        logger.debug(f"{traceback.format_exc()}")
        return JsonResponse(data={'error': f"An error occurred: {str(e)}"}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_scraped_webpages(request: Request):
    """
    API to get web pages data with pagination and search functionality
    params: Django Request object
    behavior:
        - Detects source page from HTTP_REFERER header
        - If from /ai-auto-schema: excludes summary from search and prioritizes schema_generating then schema_generated
        - If from /create-article: includes summary in search
    return: paginated list of webpage data
    """
    page = int(request.GET.get('page', 1))
    per_page = int(request.GET.get('per_page', 25))
    search = request.GET.get('search', '')
    sort = request.GET.get('sort', '')
    source = request.GET.get('source', '')

    sorting_columns_mapping = {
        'summary': "summary",
        'title': "title",
        'url': "url",
        'lastScanned': "last_scraped_on",
        'schemaFound': "schema_enabled"
    }

    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponse(status=200, data={"web_pages": [], "total_pages": 0, "total_objects": 0})

    # Fetch all webpages for the current website
    web_pages: QuerySet[WebPage] = website.webpage_set.all()

    # Apply search if provided
    if search:
        # Different search filters based on source page
        if source == '/ai-auto-schema':
            # For ai-auto-schema page: search only title and url (exclude summary)
            web_pages = web_pages.filter(
                Q(title__icontains=search) |
                Q(url__icontains=search)
            )
        elif source == '/create-article':
            # For create-article page: search title, url, and summary
            web_pages = web_pages.filter(
                Q(title__icontains=search) |
                Q(url__icontains=search) |
                Q(summary__icontains=search)
            )
        else:
            # Default behavior: search title and url (maintain backward compatibility)
            web_pages = web_pages.filter(
                Q(title__icontains=search) |
                Q(url__icontains=search)
            )

    # Apply sorting based on source and user preferences
    if source == '/ai-auto-schema':
        # For ai-auto-schema page: prioritize schema_generating, then schema_generated, then rest
        web_pages = web_pages.annotate(
            schema_priority=Case(
                When(schema_generating=True, then=Value(1)),
                When(schema_generated=True, then=Value(2)),
                default=Value(3),
                output_field=IntegerField()
            )
        )

        # Apply additional sorting if provided, but always keep schema_priority as primary sort
        if sort:
            field, direction = sort.split(':')
            db_field = sorting_columns_mapping.get(field, 'last_scraped_on')

            if direction == 'desc':
                db_field = f'-{db_field}'

            web_pages = web_pages.order_by('schema_priority', db_field)
        else:
            # Apply default secondary sorting with schema_priority as primary
            web_pages = web_pages.order_by('schema_priority', '-created_on')
    else:
        # For other sources, apply regular sorting
        if sort:
            field, direction = sort.split(':')
            db_field = sorting_columns_mapping.get(field, 'last_scraped_on')

            if direction == 'desc':
                db_field = f'-{db_field}'

            web_pages = web_pages.order_by(db_field)
        else:
            # Apply default sorting only when no custom sorting is requested
            web_pages = web_pages.order_by('-created_on')

    total_count = web_pages.count()

    if per_page == -1:
        # Fetch all data
        page_obj = web_pages
    else:
        # Apply pagination
        paginator = Paginator(web_pages, per_page)
        page_obj = paginator.get_page(page)

    # Serialize the object
    serializer = WebPageSerializer(page_obj, many=True)

    return JsonResponse(
        status=200,
        data={
            "web_pages": serializer.data,
            "total_count": total_count,
        }
    )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_webpage_schema_setting(request: Request):
    """
    API to update schema_enabled setting for a specific webpage
    params: Django Request object with url and schema_enabled
    return: success message
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_ACTIVE_WEBSITE", 'message': "No active website found."})

    try:
        url = request.data['url']
        schema_enabled = request.data['schema_enabled']
    except KeyError:
        return JsonResponseBadRequest(additional_data={'err_id': "MISSING_PARAMETERS", 'message': "Missing required parameters."})

    try:
        webpage = website.webpage_set.get(url=url)
        webpage.schema_enabled = schema_enabled
        webpage.save()

        return JsonResponse(status=200, data={'message': "Schema setting updated successfully."})

    except WebPage.DoesNotExist:
        return JsonResponseBadRequest(additional_data={'err_id': "WEBPAGE_NOT_FOUND", 'message': "Webpage not found."})

    except Exception as e:
        logger.critical(f"Error updating webpage schema setting: {str(e)}")
        return JsonResponseServerError()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_glossary_topic(request: Request):
    """
    Authenticates & returns glossary data for Glossary Generator page.
    """
    user: User = request.user
    current_plan_name =get_stripe_product_data(user)['name']
    glossary_words_limit = get_stripe_product_data(user)['metadata']['glossary_words']  # Glossary words limit

    # Check if the flag condition is met
    flag = user.glossary_contents_generated >= glossary_words_limit
    glossary_balance = max(0, glossary_words_limit - user.glossary_contents_generated)
    
    if request.method == 'GET':
        try:
            project_id: str = request.query_params['project_id']
        except KeyError:
            return JsonResponseRedirect('dashboard')

        glossary_topic = GlossaryTopic.objects.filter(project_id=project_id).first()

        if glossary_topic:
            return JsonResponse(status=200, data={
                'word': glossary_topic.word,
                'glossary_words': glossary_topic.glossary_words,
                'project_name':f"glossary-topic-{glossary_topic.word[:15]}",
                'count': len(glossary_topic.glossary_words),
                'current_plan_name':current_plan_name,
                'flag':flag,
                'glossary_balance': glossary_balance,
                'all_integrations_with_unique_id': user.all_integrations_with_unique_id,
            })
        else:
            return JsonResponse(status=200, data={
                'message': "No glossary topics found."
            })

    else:
        return JsonResponseBadRequest()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_glossary_projects(request: Request):
    """
    Authenticates & returns a list of all glossary projects filtered by project_id, with project_name and count.
    """
    user: User = request.user

    if request.method == 'GET':
        try:
            # Fetch glossary topics for the current active website and order by creation date
            glossary_topics = user.glossary_topics.filter(word__isnull=False).order_by('-created_at')


            # Create a dictionary to group glossary topics by project_id
            project_list = []
            for glossary_topic in glossary_topics:
                count = len(glossary_topic.glossary_words) if glossary_topic else 0

                # Only add the project to the list if count > 0
                if count > 0:
                    project_list.append({
                        'project_id': glossary_topic.project_id,
                        'project_name': f"glossary-topic-{glossary_topic.word[:15]}",
                        'glossary_word': glossary_topic.word,
                        'glossary_words': glossary_topic.glossary_words,
                        'count': count
                    })

            return JsonResponse(status=200, data={'projects': project_list})
        except Exception as e:
            return JsonResponse(status=400, data={'message': str(e)})
    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_or_rescan_webpage(request: Request):
    """
    API to add/rescan a website webpage
    params: Django Request object
    """
    try:
        page_url = request.data['page_url']
        rescan = request.data.get("rescan", False)
    except KeyError:
        return JsonResponseKeyError()

    user: User = request.user
    website: Website = user.current_active_website
    page_url_extract = tldextract.extract(page_url)

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_CONNECTED"})

    # Check the sitemap domain
    if not page_url_extract or f"{page_url_extract.domain}.{page_url_extract.suffix}" != website.domain:
        return JsonResponseBadRequest(additional_data={'err_id': "MISMATCH_DOMAIN"})

    if WebPage.objects.filter(url=page_url).exists() and not rescan:
        return JsonResponseBadRequest(additional_data={'err_id': "URL_ALREADY_EXISTS"})

    # Check page status
    res = requests.get(page_url)

    if not res.ok:
        return JsonResponseBadRequest(additional_data={'err_id': "NON_SUCCESS_STATUS_CODE"})

    # Add or Rescan the URL
    websiteScanning = WebsiteScanning(website, rescan=rescan)
    websiteScanning.add_or_rescan_url(page_url)

    if not DEBUG:
        celery_check_flyio_provisioning.delay(websiteScanning.machine_id,
                                              websiteScanning.website_scanning_job_id,
                                              FLY_WEBSITE_SCANNING_APP_NAME,
                                              FLY_WEBSITE_SCANNING_DEPLOY_TOKEN)

    return JsonResponse(status=200, data={'success': True, 'message': "OK" , 'job_id': websiteScanning.website_scanning_job_id})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_webpages_schema(request: Request):
    """
    API to generate or regenerate webpages schema.
    params: Django Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponse(status=200, data={'success': False,
                                              'err_id': "NO_WEBSITE_CONNECTED",
                                              'message': "No website is connected to your account."})

    try:
        generation_type = request.data['generation-type']  # regenerate, single-generate or bulk-generate
    except KeyError as e:
        return JsonResponse(status=200, data={'success': False,
                                              'err_id': "MISSING_PARAMETERS",
                                              'message': f"Missing required parameter: {str(e)}"})

    page_url = request.data.get('page_url')

    if generation_type in ['regeneration', 'single-generate'] and not page_url:
        return JsonResponse(status=200, data={'success': False,
                                              'err_id': "MISSING_PAGE_URL",
                                              'message': "Page URL is required for this operation."})

    if page_url:
        # Validate page URL belong to the user's website domain
        page_url_extract = tldextract.extract(page_url)

        # Check the domain matches
        if not page_url_extract:
            return JsonResponse(status=200, data={'success': False,
                                                  'err_id': "INVALID_URL",
                                                  'message': f"The provided URL '{page_url}' is not valid."})

        # Extract the domain from page url
        if page_url_extract.subdomain:
            page_url_domain = f"{page_url_extract.subdomain}.{page_url_extract.domain}.{page_url_extract.suffix}"
        else:
            page_url_domain = f"{page_url_extract.registered_domain}"

        if page_url_domain != website.domain:
            return JsonResponse(status=200, data={'success': False,
                                                  'err_id': "MISMATCH_DOMAIN",
                                                  'message': f"The URL '{page_url}' does not belong to your website domain."})

        # Check if the webpage exists
        try:
            # Fetch the webpage
            webpage = website.webpage_set.get(url=page_url)
        except WebPage.DoesNotExist:
            return JsonResponse(status=200, data={'success': False,
                                                  'err_id': "WEBPAGE_NOT_FOUND",
                                                  'message': f"Webpage '{page_url}' was not found in your website."})

        if generation_type == 'single-generate' and webpage.schema_generated:
            return JsonResponse(status=200, data={'success': False,
                                                  'err_id': "SCHEMA_ALREADY_GENERATED",
                                                  'message': f"Schema has already been generated for '{page_url}'."})

    run_parameter = 'generate-schema'

    # TODO: Add limit check based on user's plan

    if generation_type == 'bulk-generate':
        # Fetch all webpages which don't have schema
        webpages = website.webpage_set.filter(schema_generated=False)[:100]  # TODO: Make this dynamic based on user's plan

    elif generation_type == 'single-generate':
        # Fetch the webpage
        webpages = website.webpage_set.filter(url=page_url)

    elif generation_type == 'regenerate':
        # Fetch the webpage
        webpages = website.webpage_set.filter(url=page_url)

    # Mark the webpages schema as generating
    webpages.update(schema_generating=True)

    # Create website scanning job for bulk regeneration
    webpage_urls = list(webpages.values_list('url', flat=True))
    website_urls_with_dates = [(url, None) for url in webpage_urls]
    websiteScanning = WebsiteScanning(website,
                                        website_urls=website_urls_with_dates,
                                        rescan=True,
                                        run=run_parameter)

    # Set the urls for processing
    websiteScanning.website_filtered_urls = website_urls_with_dates

    # Create the crawling task
    websiteScanning.create_website_crawling_task()

    if not DEBUG:
        celery_check_flyio_provisioning.delay(websiteScanning.machine_id,
                                                websiteScanning.website_scanning_job_id,
                                                FLY_WEBSITE_SCANNING_APP_NAME,
                                                FLY_WEBSITE_SCANNING_DEPLOY_TOKEN)

    return JsonResponse(status=200, data={
        'success': True,
        'message': "OK",
        'job_id': websiteScanning.website_scanning_job_id,
        'pages_urls': webpage_urls,
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_glossary_words(request: Request):
    """
    Updates glossary words for a specific project.
    """
    user: User = request.user
    project_id = request.data.get('project_id')
    glossary_words = request.data.get('glossary_words')
    word = request.data.get('word')
    website: Website = user.current_active_website

    # Validate required fields
    if not project_id or not glossary_words or not word or not website:
        return JsonResponseBadRequest("Missing required fields: project_id, glossary_words, word, or website.")

    # Update or create GlossaryTopic
    glossary_topic, created = GlossaryTopic.objects.get_or_create(
        project_id=project_id,
        defaults={'word': word, 'glossary_words': glossary_words, 'website': website}
    )

    if not created:
        glossary_topic.word = word
        glossary_topic.glossary_words = glossary_words
        glossary_topic.website = website
        glossary_topic.save()

    return JsonResponse(status=200, data={
        'message': "Glossary words updated successfully.",
        'glossary_topic': {
            'word': glossary_topic.word,
            'glossary_words': glossary_topic.glossary_words,
            'project_id': glossary_topic.project_id,
        }
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_bulk_glossary_content(request: Request):
    user: User = request.user
    project_id = request.data.get('project_id')
    glossary_words = request.data.get('words',[])

    if not project_id:
        return JsonResponse(
            status=400,
            data={"error": "'project_id' is a required field."}
        )

    # Check user limits
    product_data = get_stripe_product_data(user)
    max_glossary_words_allowed = product_data['metadata']['glossary_words']  # Maximum glossary words allowed
    if (user.glossary_contents_generated) >= max_glossary_words_allowed:
            return JsonResponse(
            status=200,
            data={
                'status': "rejected",
                'reason': 'max_limit_reached',
                'balance_title': max_glossary_words_allowed,
            }
    )
    try:
        # Enqueue the Celery task
        task = generate_bulk_glossary_content_task.delay(user.id, project_id, glossary_words)

        # Update task_id for all glossary words for the given project_id
        if not glossary_words:
            glossary_contents = GlossaryContent.objects.filter(project_id=project_id)
            glossary_contents.update(task_id=task.id)

        return JsonResponse(
            status=202,
            data={"message": "Task has been queued.", "task_id": task.id}
        )
    except Exception as e:
        logger.error(f"Error queuing task: {str(e)}")
        return JsonResponse(
            data={"error": f"An error occurred: {str(e)}"},
            status=500,
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def resume_bulk_glossary_content(request: Request):
    user = request.user
    task_id = request.data.get('task_id')

    if not task_id:
        return JsonResponse(
            status=400,
            data={"error": "'task_id' is a required field."}
        )

    try:
        # Validate if the task exists in the database
        glossary_entry = GlossaryContent.objects.filter(task_id=task_id).first()

        if not glossary_entry:
            return JsonResponse(
                status=404,
                data={"error": "Task ID not found in the database."}
            )

        project_id = glossary_entry.project_id

        # Validate Celery task state
        celery_task = current_app.AsyncResult(task_id)
        if celery_task.state not in ["FAILURE","REVOKED"]:
            return JsonResponse(
                status=400,
                data={"error": "The task is not in a failed or revoked state and cannot be retried."}
            )

        # Re-queue the task
        new_task = generate_bulk_glossary_content_task.delay(user.id, project_id)

        # Update task_id in the database for the new task
        GlossaryContent.objects.filter(project_id=project_id).update(task_id=new_task.id)

        return JsonResponse(
            status=202,
            data={
                "message": "Task has been re-queued successfully.",
                "new_task_id": new_task.id
            }
        )
    except Exception as e:
        logger.error(f"Error resuming task: {str(e)}")
        return JsonResponse(
            data={"error": f"An error occurred: {str(e)}"},
            status=500,
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def Get_Glossary_Terms(request: Request):
    """
    Get glossary terms along with their generated content and the count of terms for a given project_id.
    """
    user = request.user
    project_id = request.query_params.get('project_id')

    product_data = get_stripe_product_data(user)
    max_glossary_words_allowed = product_data['metadata']['glossary_words']  # Maximum glossary words allowed
    has_exceeded_glossary_limit  = (user.glossary_contents_generated) >= max_glossary_words_allowed
    glossary_balance = max(0, max_glossary_words_allowed - user.glossary_contents_generated)

    if not project_id:
        return JsonResponse(
            status=400,
            data={"error": "'project_id' is a required query parameter."}
        )

    try:
        # Fetch glossary content for the given project_id
        glossary_contents = GlossaryContent.objects.filter(project_id=project_id)

        if not glossary_contents.exists():
            return JsonResponse(
                status=404,
                data={"error": "No glossary content found for the given project_id."}
            )

        # Serialize data with limited fields
        glossary_data = GlossaryTopicSerializer(glossary_contents, many=True).data

        return JsonResponse(
            status=200,
            data={
                "message": "Glossary terms successfully retrieved.",
                "glossary_terms": glossary_data,
                "count":glossary_contents.count(),
                "has_exceeded_glossary_limit" : has_exceeded_glossary_limit,
                "glossary_balance": glossary_balance,
            }
        )

    except Exception as e:
        logger.error(f"Error fetching glossary terms: {str(e)}")
        logger.debug(f"{traceback.format_exc()}")
        return JsonResponse(
            data={"error": f"An error occurred: {str(e)}"},
            status=500,
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def Get_Glossary_By_Keyword_Hash(request: Request):
    """
    Get glossary term and its generated content based on keyword_hash.
    """
    user: User = request.user
    keyword_hash = request.query_params.get('keyword_hash')

    if not keyword_hash:
        return JsonResponse(
            status=400,
            data={"error": "'keyword_hash' is a required query parameter."}
        )

    try:
        # Fetch glossary content based on the provided keyword_hash
        glossary_content = GlossaryContent.objects.filter(keyword_hash=keyword_hash).first()

        if not glossary_content:
            return JsonResponse(
                status=404,
                data={"error": "No glossary content found for the given keyword_hash."}
            )

        # Convert the model instance to a dictionary using serializer
        glossary_data = GlossaryContentSerializer(glossary_content).data

        # Add additional data if needed
        glossary_data['all_integrations_with_unique_id'] = list(user.all_integrations_with_unique_id)

        try:
           schedule_article_posting = ScheduleGlossaryPosting.objects.get(glossary=glossary_content)
        except ScheduleGlossaryPosting.DoesNotExist:
                schedule_article_posting = None

                if schedule_article_posting:
                    glossary_data['article_scheduled_for_posting'] = True
                    glossary_data['article_scheduled_datetime'] = schedule_article_posting.schedule_datetime.isoformat()
                else:
                    glossary_data['article_scheduled_for_posting'] = False
                    glossary_data['article_scheduled_datetime'] = None


        return JsonResponse(
            status=200,
            data={
                "message": "Glossary term successfully retrieved.",
                "glossary_term": glossary_data,
            }
        )

    except Exception as e:
        logger.error(f"Error fetching glossary term by keyword_hash: {str(e)}")
        logger.debug(f"{traceback.format_exc()}")
        return JsonResponse(
            data={"error": f"An error occurred: {str(e)}"},
            status=500,
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def post_glossary_api(request: Request):
    """
    Posts article to user's website based on integration settings. If user has not set up any integration, returns
    bad request (status code 400).

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            keyword_hash: str = request.data['keyword_hash']
            selected_integration: str = request.data['selected_integration']
            selected_integration_unique_text_id: str = request.data.get('selection_integration_id')
            post_status: Literal['draft', 'publish'] = request.data.get('post_status', 'publish')
            selected_categories = request.data.get('selected_categories')
            update_published_article = request.data.get('update_published_article', False)

        except KeyError:
            return JsonResponseBadRequest()

        try:
            # glossary: GlossaryContent = user.glossary_content.get(keyword_hash=keyword_hash)
            glossary = GlossaryContent.objects.filter(keyword_hash=keyword_hash).first()
        except GlossaryContent.DoesNotExist:
            logger.error(f"No such glossary exists (keyword hash: {keyword_hash})")
            return JsonResponseBadRequest()

        if selected_integration not in user.all_integrations:
            logger.error(f"'{user.email}' selected integration '{selected_integration}' is not supported.")
            return JsonResponseBadRequest()

        if "wordpress" in selected_integration and user.wordpress_integrations.exists():
            res = publish_glossary_to_wp(glossary,
                                        user,
                                        wp_site_url=selected_integration_unique_text_id,
                                        status=post_status,
                                        selected_categories=selected_categories,
                                        update_published_article=update_published_article)

            if res["status"] == "success":
                logger.debug(f"Glossary {glossary.keyword_hash} posted successfully!")
                return JsonResponse(status=200, data={
                    'link': res["glossary_link"],
                    'keyword_hash': glossary.keyword_hash,
                    'posted_to': glossary.posted_to,
                    'posted_on': glossary.posted_on,
                })

            else:
                logger.error(f"Glossary posting failed. Error message: {res['error_message']}")

                # send email notification
                email_message = unable_to_publish_article_body(user.username)
                send_email(
                    user.email,
                    ABUN_NOTIFICATION_EMAIL,
                    "Team Abun",
                    "Glossary couldn't be published to your blog",
                    email_message
                )

                return JsonResponseServerError()
    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def save_glossary_api(request: Request):
    """
    Saves updated article content.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == 'POST':
        try:
            keyword_hash: str = request.data['keyword_hash']
            content: str = request.data['content']
            feedback: str = request.data['feedback'] if 'feedback' in request.data else None
        except KeyError:
            return JsonResponseBadRequest()

        if content == '':
            return JsonResponseBadRequest()

        try:
            glossary = GlossaryContent.objects.filter(keyword_hash=keyword_hash).first()
        except Article.DoesNotExist:
            return JsonResponseRedirect("dashboard")

        glossary.content = content

        if feedback:
            glossary.feedback = feedback

        glossary.save()

        return JsonResponse(status=200, data={'message': "OK"})

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def schedule_glossary_auto_publish(request: Request):
    """
    Schedules an glossary for auto publish
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user

    try:
        keyword_hash: str = request.data['keyword_hash']
        schedule_datetime_string: str = request.data['schedule_date']
        selected_integration: str = request.data['selected_integration']
        selected_integration_unique_id: str = request.data.get('selected_integration_id')
        post_status: Literal['draft', 'publish'] = request.data.get('post_status', 'publish')

    except KeyError:
        return JsonResponseBadRequest()

    try:
        schedule_datetime = datetime.datetime.fromisoformat(schedule_datetime_string)
        current_datetime = datetime.datetime.now(tz=ZoneInfo('UTC'))
    except ValueError:
        return JsonResponse(status=200, data={'success': False, 'message': "Schedule date is not in valid ISO format."})

    if selected_integration not in user.all_integrations:
        return JsonResponse(status=200, data={'success': False, 'message': f"Please connect your '{selected_integration}' "
                                                                           "account to schedule an article for publishing."})


    elif current_datetime > schedule_datetime or (schedule_datetime - current_datetime).days > 100:
        return JsonResponse(status=200, data={'success': False, 'message': f"Please select a valid schedule date & time."})

    try:
        glossary = GlossaryContent.objects.filter(keyword_hash=keyword_hash).first()
    except Article.DoesNotExist:
        return JsonResponse(status=200, data={'success': False, 'message': f"No glossary found with '{keyword_hash}' keywordHash."})

    if not glossary.user.email == user.email:
        logger.critical(f"'{keyword_hash}' belongs to the user '{glossary.user.email}', but "
                        f"'{user.email}' tried to schedule this article for auto publish.")
        return JsonResponse(status=400, data={'success': False, 'message': f"No glossary found with '{keyword_hash}' keywordHash."})

    elif glossary.is_posted:
        return JsonResponse(status=200, data={'success': False, 'message': f"Glossary is already published to your site."})

    schedule_article_posting, _ = ScheduleGlossaryPosting.objects.get_or_create(glossary=glossary)
    schedule_article_posting.schedule_datetime = schedule_datetime
    schedule_article_posting.selected_integration_name = selected_integration
    schedule_article_posting.selected_integration_unique_text_id = selected_integration_unique_id
    schedule_article_posting.post_status = post_status
    schedule_article_posting.save()


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def find_website_sitemaps(request: Request):
    """
    API to find website sitemaps
    params: Django Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_CONNECTED"})

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if not user.verified and current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={'err_id': "TRIAL_PLAN"})

    website_domain: str = website.domain

    if not website.sitemap_urls:
        # Update website sitemaps info
        website.finding_sitemaps = True
        website.save()

        # Create WebsiteScanning object and fetch website sitemaps and URLs
        websiteScanning = WebsiteScanning(website)

        try:
            websiteScanning.fetch_website_sitemaps_and_urls()
            sitemaps = websiteScanning.website_sitemaps
        except TimeoutError:
            sitemaps = []

        website.finding_sitemaps = False
        website.sitemap_urls = sitemaps
        website.save()

        # Start crawling the website
        if sitemaps and not website.is_crawled:
            # Delegate task to celery
            celery_start_website_scanning.delay(website_domain,
                                                sitemaps,
                                                websiteScanning.website_urls)

    else:
        sitemaps = website.sitemap_urls

    if sitemaps:
        return JsonResponse(status=200, data={'success': True, 'sitemaps': sitemaps})
    else:
        return JsonResponse(status=200, data={'success': False, 'sitemaps': []})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_website_analysis_stats(request: Request):
    """
    API to fetch website analysis stats from Redis
    params: Django Request object
    returns: Website analysis stats or appropriate error message
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_CONNECTED"})

    try:
        with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
            stats_key = f"{website.domain}_{user.email}"
            stats_data = redis_connection.get(stats_key)

            if not stats_data:
                # If no stats found, return default values
                return JsonResponse(status=200, data={
                    'success': True,
                    'stats': {
                        'total_pages': 0,
                        'pages_scanned': 0,
                        'progress': 0,
                        'estimated_time_left': 0,
                        'time_display': "~0",
                        'steps': {
                            'crawling': 'pending',
                            'analyzing': 'pending',
                            'generating': 'pending'
                        },
                        'is_analysis_complete': False
                    }
                })

            # Decode Redis data
            stats = json.loads(stats_data)

            # Add a flag to indicate if analysis is complete
            is_analysis_complete = (
                stats['progress'] == 100 and
                stats['steps']['crawling'] == 'completed' and
                stats['steps']['analyzing'] == 'completed' and
                stats['steps']['generating'] == 'completed'
            )

            # Format time remaining for better readability
            # Format time remaining for better readability
            seconds_left = stats['estimated_time_left']
            if seconds_left > 0:
                if seconds_left < 60:
                    time_display = "~1"
                else:
                    minutes_left = math.ceil(seconds_left / 60)
                    if minutes_left > 60:
                        hours = minutes_left // 60
                        mins = minutes_left % 60
                        time_display = f"~{hours} hour{'s' if hours > 1 else ''}" + (f" {mins}" if mins > 0 else "")
                    else:
                        time_display = f"~{minutes_left}"
            else:
                time_display = "~0"

            response_data = {
                'success': True,
                'stats': {
                    **stats,
                    'is_analysis_complete': is_analysis_complete,
                    'time_display': time_display
                }
            }

            return JsonResponse(status=200, data=response_data)

    except redis.RedisError as e:
        logger.error(f"Redis error while fetching website stats: {str(e)}")
        return JsonResponse(
            status=503,
            data={
                'success': False,
                'message': 'Unable to fetch analysis stats',
                'err_id': 'REDIS_ERROR'
            }
        )

    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error while parsing website stats: {str(e)}")
        return JsonResponse(
            status=500,
            data={
                'success': False,
                'message': 'Invalid stats data format',
                'err_id': 'INVALID_STATS_FORMAT'
            }
        )

    except Exception as e:
        logger.error(f"Unexpected error while fetching website stats: {str(e)}")
        return JsonResponse(
            status=500,
            data={
                'success': False,
                'message': 'An unexpected error occurred',
                'err_id': 'UNEXPECTED_ERROR'
            }
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def rescrape_website_pages(request: Request):
    """
    API to re-scrape website pages
    params: Django Request object
    """
    if not DEBUG:
        return JsonResponse404()

    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_CONNECTED"})

    website_domain: str = website.domain

    # Delegate task to celery
    celery_start_website_scanning.delay(website_domain, rescrape=True)

    return JsonResponse(status=200, data={'success': True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def scrape_more_website_pages(request: Request):
    """
    API to scrape more website pages
    params: Django Request object
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_CONNECTED"})

    if not website.has_more_pages:
        return JsonResponseBadRequest(additional_data={'err_id': "ALL_PAGES_SCRAPED"})

    website_domain: str = website.domain

    # Delete redis key
    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        stats_key = f"{website.domain}_{user.email}"
        redis_connection.delete(stats_key)

    if not website.pages_scan_limit_reached:
        # Delegate task to celery
        celery_start_website_scanning.delay(website_domain)

    return JsonResponse(status=200, data={'success': True})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_custom_keyword_for_keyword_api(request: Request):
    """
    API to create custom keyword
    params: Django Request object
    """
    user: User = request.user

    try:
        try:
            project_id: str = html.escape(request.data["projectId"].strip())
            custom_keyword: str = html.escape(
                request.data["customKeyword"].strip()
            )
        except KeyError:
            return JsonResponseBadRequest()

        try:
            # Check if the user has reached the max limit for keywords
            max_keywords_allowed: int = get_stripe_product_data(user)['metadata']['max_keywords']
            if (user.keywords_generated) >= max_keywords_allowed:
                logger.error(f"upload_keywords_api_v2() - Max limit reached for user {user.email}")
                return JsonResponse(status=200, data={"status": "rejected", 'reason': "max_limit_reached", 'message': 'max limit reached'})
        except Exception as err:
            logger.critical(f"upload_keywords_api_v2() - Error while checking max limit for user {user.email}: {err}")
            return JsonResponseServerError()

        try:
            keyword_projects_object = user.keyword_projects.get(
                project_id=project_id
            )
        except KeywordProject.DoesNotExist:
            logger.error(f"Project id was not found in user Keyword Project")
            return JsonResponseBadRequest(
                additional_data={"err_id": "NO SUCH KEYWORD PROJECT FOUND"}
            )
        except Exception as err:
            logger.critical(err)
            return JsonResponseServerError()

        keyword_list = [custom_keyword]

        # Fetch keyword data in batches
        try:
            keyword_data_list = fetch_keyword_data(keyword_list)
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=500)

        # Process fetched keyword data
        for keyword_data in keyword_data_list:
            cleaned_keyword = re.sub(r'[^a-zA-Z0-9\s]', "", keyword_data['keyword'])
            keyword_object = create_keyword_object(
                user=user,
                keyword=unescape_amp_char(cleaned_keyword),
                with_default_values=False,  # Set to False since we have real data
                source=keyword_data['source'],  # This will be 'keywordseverywhere'
                country=keyword_data['country'],  # This will be 'global'
                serp_position=keyword_data['serp_position'],  # This will be None
                volume=keyword_data['volume'],
                cpc_currency="USD",  # Assuming you have this field
                cpc_value=keyword_data['cpc'],
                paid_difficulty=keyword_data['paid_difficulty'],
                trend=keyword_data['trend'],
            )
            keyword_object.save()

        keyword_projects_object.keywords.add(keyword_object)
        keyword_projects_object.save()

        user.keywords_generated += 1
        user.total_keywords_generated += 1
        user.save()

        return JsonResponse(status=200, data={"message": "OK"})
    except Exception as err:
            logger.critical(err)
            return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def remove_keyword(request: Request):
    """
    API to remove keyword
    params: Django Request object
    """
    user: User = request.user
    try:
        keyword_hash : str = html.escape(request.data["keywordHash"].strip())
        keyword : str = html.escape(request.data["keyword"].strip())
    except KeyError:
        return JsonResponseBadRequest()

    try:
        keyword_object = user.keywords.get(
            keyword_md5_hash=keyword_hash, keyword=keyword
        )
        keyword_project_object = user.keyword_projects.get(keywords=keyword_object)
    except Keyword.DoesNotExist:
        logger.error(f"Keyword with md5 hash '{keyword_hash}' was not found in user keywords")
        return JsonResponseBadRequest(
            additional_data={"err_id": "NO_SUCH_KEYWORD_FOUND"}
        )
    except Exception as err:
        logger.critical(err)
        return JsonResponseServerError()

    generated = user.articles.filter(keyword=keyword, is_generated=True).exists()

    exists_and_off = user.automation_projects.filter(
        associated_keyword_project=keyword_project_object,
        auto_publish_state='off'
    ).exists()

    # Check if no project exists for the given keyword project
    no_projects_exist = not user.automation_projects.filter(
        associated_keyword_project=keyword_project_object
    ).exists()


    if not generated and (exists_and_off or no_projects_exist):
        try:
            # Try to retrieve the keyword object to delete it
            keyword_object.delete()
            return JsonResponse({"message": "Keyword deleted successfully"}, status=200)
        except Exception as e:
            return JsonResponse({"error": "Keyword to delete not found"}, status=400)
    return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def remove_keyword_project(request: Request):
    """
    API to remove keyword
    params: Django Request object
    """
    user: User = request.user
    try:
        keyword_project_id : str = html.escape(request.data["projectId"].strip())
    except KeyError:
        return JsonResponseBadRequest()

    try:
        keyword_project_object = user.keyword_projects.get(
            project_id=keyword_project_id
        )
    except KeywordProject.DoesNotExist:
        logger.error(f"Keyword project with project Id  '{keyword_project_id}' was not found in user keyword Project")
        return JsonResponseBadRequest(
            additional_data={"err_id": "NO_SUCH_KEYWORD_FOUND"}
        )
    except Exception as err:
        logger.critical(err)
        return JsonResponseServerError()

    generated = user.articles.filter(keyword=keyword_project_object.keywords, is_generated=True).exists()

    exists_and_off = user.automation_projects.filter(
        associated_keyword_project=keyword_project_object,
        auto_publish_state='off'
    ).exists()

    # Check if no project exists for the given keyword project
    no_projects_exist = not user.automation_projects.filter(
        associated_keyword_project=keyword_project_object
    ).exists()

    if not generated and (exists_and_off or no_projects_exist):
        try:
            # Try to retrieve the keyword object to delete it
            keyword_project_object.delete()
            return JsonResponse({"message": "Keyword project deleted successfully"}, status=200)
        except Exception as e:
            return JsonResponse({"error": "Keyword project to delete not found"}, status=400)

    return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def validate_blog_urls(request: Request):
    """
    Validate if the given URLs correspond to valid blog posts using the Serper API.

    Args:
        blog_urls (list): List of blog URLs to validate.
        max_results (int): Maximum number of results to check for each blog URL.

    Returns:
        dict: A dictionary where the keys are the input URLs and values are validation results (True/False with details).
    """
    user: User = request.user
    blog_urls = request.data.get('blog_urls',"")
    uploaded_file = request.FILES.get('file')
    selected_location = json.loads(request.data['selectedLocation'])
    country: str = selected_location['country_iso_code'].lower()
    header: str = request.data.get("header","")
    website: Website = user.current_active_website
    max_emails = get_stripe_product_data(user)['metadata']['max_emails']

    try:
        if blog_urls:
            blog_urls_list = blog_urls.splitlines()
        elif uploaded_file:
            blog_urls_list = extract_blog_urls(uploaded_file, header)
        else:
            blog_urls_list = []
    except ValueError as e:
        return JsonResponse({'status': "error",'message': "check extension(.csv , .xlsx, .xlx), row limit is 200 and header to be blog_urls"}, status=400)
    blog_objects = []

    if max_emails < (user.blog_emails_found + len(blog_urls_list)):
        return JsonResponse({'status': "rejected",'message': "max email limit reached"}, status=402)

    if isinstance(blog_urls_list, list):
        # Use ThreadPoolExecutor to process URLs concurrently
        with ThreadPoolExecutor(max_workers=min(len(blog_urls_list), 32)) as executor:
            results = executor.map(
                process_blog_url, blog_urls_list, [country] * len(blog_urls_list), [website] * len(blog_urls_list), [user] * len(blog_urls_list)
            )
            blog_objects = [result for result in results if result is not None]

        with transaction.atomic(using='default'):
            BlogFinder.objects.bulk_create(blog_objects)

        if uploaded_file:

            file_name = uploaded_file.name.split(".")[0]
            first_five = file_name[:5] if len(file_name) >= 5 else file_name
            with transaction.atomic(using='default'):

                existing_blog = BlogFinder.objects.filter(website=website,
                                                            blog_m5_hash__in=[blog.blog_m5_hash for blog in blog_objects])
                blog_find_project = BlogFinderProject.objects.create(
                    website=website,
                    blog_project_id=str(uuid.uuid4())[:16],
                    blog_project_name=unescape_amp_char("Blog_Proj_" + first_five)
            )
                blog_find_project.blog_finder.add(*existing_blog)
                blog_find_project.save()
    else:
        return JsonResponse(status=200, data={"error": "Invalid data"})
    return JsonResponse(status=200, data= {'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_blog_data(request: Request):
    """
    Get Blog data api
    """
    user: User = request.user
    blog_project_id = request.query_params.get('blogProjectId', None)
    try:
        if blog_project_id:
            blog_project_obj = BlogFinderProject.objects.filter(blog_project_id=blog_project_id).first()
            blog_url_data = blog_project_obj.blog_finder.all()
        else:
            blog_url_data = BlogFinder.objects.filter(
                website=user.current_active_website
            ).exclude(
                blog_finder_project__isnull=False
            ).order_by('-created_at')

        blog_serialized_data = BlogFinderSerializer(blog_url_data, many=True).data

        return JsonResponse({'status': 'success', 'data': blog_serialized_data}, status=200)
    except Exception as e:
        logger.critical("An error occurred in get_blog_data: %s", str(e), exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Internal Server Error'},status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_blog_bulk_data(request: Request):
    """
    Get Bulk Blog data api
    """
    user: User = request.user
    try:
        blog_project = BlogFinderProject.objects.filter(
            website=user.current_active_website
        ).order_by('-created_at')
        blog_bulk_serialized_data = BlogFinderProjectSerializer(blog_project, many=True).data

        return JsonResponse({'status': 'success', 'data': blog_bulk_serialized_data}, status=200)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def download_blog_data_as_csv(request: Request):
    """
    Download blog csv API
    """
    try:
        blog_project_id: str = request.query_params['blogProjectId']
        if not blog_project_id:
            return JsonResponseBadRequest({"error": "blog_project_id is required"})
    except Exception as e:
        return JsonResponseBadRequest({"error": "Invalid request data"})

    blog_project_obj = BlogFinderProject.objects.filter(blog_project_id=blog_project_id).first()
    if not blog_project_obj:
        return JsonResponseBadRequest({"error": "Blog project not found"})

    blog_url_data = blog_project_obj.blog_finder.all()

    download_csv_serialized_data = BlogFinderSerializer(blog_url_data, many=True).data

    return JsonResponse({'status': 'success', 'data': download_csv_serialized_data}, status=200)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def edit_glossary_term_api(request: Request):
    """
    Saves updated glossary title. Returns new title value on success.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    if request.method == "POST":
        try:
            keyword_hash: str = request.data["keyword_hash"]
            term: str = html.escape(request.data["term"].strip())
        except KeyError:
            return JsonResponseBadRequest()

        try:
            glossary = GlossaryContent.objects.filter(keyword_hash=keyword_hash).first()
        except GlossaryContent.DoesNotExist:
            return JsonResponseBadRequest(additional_data={"err_id": "NO_SUCH_GLOSSARY"})

        glossary_topic = GlossaryTopic.objects.filter(project_id=glossary.project_id).first()

        if glossary_topic:
                glossary_words = glossary_topic.glossary_words
                if glossary.term in glossary_words:
                    index = glossary_words.index(glossary.term)
                    glossary_words[index] = term  # Update the term at the same index

        glossary_topic.glossary_words = glossary_words
        glossary_topic.save()

        glossary.term = term
        glossary.save()


        return JsonResponse(status=200, data={
            'term': unescape_amp_char(glossary.term)
        })

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def bulk_post_glossary_api(request: Request):
    """
    Bulk posts glossary to user's website based on integration settings. If user has not set up any integration, returns
    bad request (status code 400).

    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    glossary_links: List[str] = []
    failed_keywords_hash: List[str] = []

    try:
        keywords_hash: List[str] = request.data['keywords_hash']
        selected_integration: str = request.data['selected_integration']
        selected_integration_unique_text_id: str = request.data['selected_integration_id']
    except KeyError:
        return JsonResponseBadRequest()

    glossaries_list: QuerySet[GlossaryContent] = user.glossary_content.filter(keyword_hash__in=keywords_hash,
                                                            is_generated=True)

    # Check integrations
    if not user.all_integrations:
        logger.critical(f"Could not post glossary to user's website {user.username} due to bad integration")
        return JsonResponseServerError()

    if not glossaries_list:
        return JsonResponseBadRequest()

    if "wordpress" in selected_integration and user.wordpress_integrations.exists():
        for glossary in glossaries_list:
            res = publish_glossary_to_wp(glossary, user, wp_site_url=selected_integration_unique_text_id)

            if res["status"] == "success":
                logger.debug(f"Glossary {glossary.keyword_hash} posted successfully!")
                glossary_links.append(res["glossary_link"])

            else:
                logger.error(f"Glossary posting failed. Error message: {res['error_message']}")
                failed_keywords_hash.append(glossary.keyword_hash)

    else:
        logger.critical(f"Could not post glossary to user's website due to bad integration")
        return JsonResponseServerError()

    # send email notification if any article is failed to publish
    if selected_integration == "wordpress" and user.wordpress_integrations.exists() and failed_keywords_hash:
        email_message: str = unable_to_publish_article_body(user.username)
        send_email(
            user.email,
            ABUN_NOTIFICATION_EMAIL,
            "Team Abun",
            "Glossary couldn't be published to your blog",
            email_message
        )

    if glossary_links:
        return JsonResponse(status=200, data={'links': glossary_links, 'keywords_hash':keywords_hash})

    else:
        return JsonResponseServerError()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def bulk_archive_unarchive_glossaries(request: Request):
    """
    Archive the Glossaries
    """
    user: User = request.user

    try:
        keywords_hash: str = request.data['keywords_hash']
        archive_type: str = request.data['archive_type']
    except KeyError:
        return JsonResponseBadRequest()

    if archive_type == 'archive':
        archive = True
    elif archive_type == 'unarchive':
        archive = False
    else:
        return JsonResponseBadRequest()

    glossaries: QuerySet[GlossaryContent] = user.glossary_content.filter(
        keyword_hash__in=keywords_hash
    )

    # exclude articles that are currently processing
    glossaries = glossaries.exclude(is_processing=True)

    # mark the articles as archived
    glossaries.update(is_archived=archive)

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def post_guest_post_finder_query(request: Request):
    """
    API to post query to guest post finder
    """
    user: User = request.user
    website: Website = user.current_active_website

    try:
        query = unescape_amp_char(request.data['query'])
        limit = request.data['limit']
    except KeyError as err:
        return JsonResponseBadRequest({'err_id': "MISSING_REQUIRED_KEY", 'message': f"Missing required key {err}."})

    # Check if the month has changed and reset gpf_queries_generated
    current_month = timezone.now().month
    last_reset_month = user.last_gpf_queries_reset.month

    if current_month != last_reset_month:
        # Reset gpf_queries_generated and update last_glossary_reset
        user.gpf_queries_generated = 0
        user.last_gpf_queries_reset = timezone.now()
        user.save()

    max_gpf_allowed: int = get_stripe_product_data(user)['metadata']['gpf_queries']

    if (user.gpf_queries_generated) >= max_gpf_allowed:
            return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_limit_reached', 'balance_title': max_gpf_allowed})

    # if GuestPostFinderQuery.objects.filter(website=user.current_active_website, query=query).exists():
    #     return JsonResponse(status=200, data={'success': False, 'message': "Query already exists."})

    guest_post_finder_query = GuestPostFinderQuery.objects.create(
        guest_project_id=str(uuid.uuid4())[:16],
        query=query,
        limit=limit,
        website=website,
    )

    task = celery_guest_post_finder.delay(website_domain=website.domain, query=query)

    return JsonResponse(status=200, data={'success': True, 'message': "Query created.",'task_id': task.id,
                                          'id': guest_post_finder_query.guest_project_id})


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_guest_post_finder_queries(request: Request):
    """
    API to get queries for guest post finder
    """
    user: User = request.user
    max_gpf_allowed: int = get_stripe_product_data(user)['metadata']['gpf_queries']
    current_plan_name = get_stripe_product_data(user)['name']
    guest_post_finder_queries = GuestPostFinderQuery.objects.filter(website=user.current_active_website).exclude(
        guest_project_id__isnull=True
    ).values(
        'guest_project_id', 'query', 'limit', 'is_processing', 'created_at').order_by("-created_at")
    queries = GuestPostFinderQuerySerializer(guest_post_finder_queries, many=True).data
    return JsonResponse(status=200,
        data={'success': True,
              'queries': queries,
              'gpf_queries_generated': user.gpf_queries_generated,
              'current_plan_name':current_plan_name,
              'max_gpf_allowed':max_gpf_allowed})


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_detail_guest_post_finder_queries(request: Request):
    """
    View to fetch GuestPostFinderResult details along with Hypestat data based on 'id' query parameter.
    """
    user : User = request.user
    website = user.current_active_website
    query_id = request.query_params.get('id')
    if not query_id:
        return JsonResponse({'success': False, 'message': "'id' query parameter is required."}, status=400)

    results = GuestPostFinderResult.objects.filter(
        guest_post_finder__guest_project_id=query_id,
        guest_post_finder__website=website
    ).select_related('hypestat', 'guest_post_finder')

    if not results.exists():
        return JsonResponse({'success': False, 'message': 'No results found for this user and query ID.'}, status=404)

    # Serialize data
    serializer = GuestPostFinderResultSerializer(results, many=True)
    return JsonResponse(status=200, data={'success': True, 'queries': serializer.data})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def post_reddit_finder_query(request: Request):
    """
    API to post query to reddit post finder
    """
    user: User = request.user
    website: Website = user.current_active_website
    try:
        query = request.data['query']
        limit = request.data['limit']
    except KeyError as err:
        return JsonResponseBadRequest({'err_id': "MISSING_REQUIRED_KEY", 'message': f"Missing required key {err}."})

    # Check if the month has changed and reset rpf_queries_generated
    current_month = timezone.now().month
    last_reset_month = user.last_rpf_queries_reset.month

    if current_month != last_reset_month:
        # Reset rpf_queries_generated and update last_reddit_reset
        user.rpf_queries_generated = 0
        user.last_rpf_queries_reset = timezone.now()
        user.save()

    max_rpf_allowed: int = get_stripe_product_data(user)['metadata']['rpf_queries']

    if (user.rpf_queries_generated) >= max_rpf_allowed:
        return JsonResponse(status=200, data={'status': "rejected", 'reason': 'max_limit_reached', 'balance_title': max_rpf_allowed})


    # Create the RedditPostFinderQuery entry
    reddit_post_finder_query = RedditPostFinderQuery.objects.create(
        reddit_project_id=str(uuid.uuid4())[:16],
        query=query,
        limit=limit,
        website=website,
    )

    # Celery task for processing
    task = celery_reddit_post_finder.delay(website_domain=website.domain, query=query)

    return JsonResponse(status=200, data={'success': True, 'message': "Query created.", 'task_id': task.id,
                                          'id': reddit_post_finder_query.reddit_project_id})


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_reddit_post_finder_queries(request: Request):
    """
    API to get queries for reddit post finder
    """
    user: User = request.user
    max_rpf_allowed: int = get_stripe_product_data(user)['metadata']['rpf_queries']
    reddit_post_finder_queries = RedditPostFinderQuery.objects.filter(website=user.current_active_website).exclude(
    reddit_project_id__isnull=True
    ).values(
        "reddit_project_id", 'query', 'limit', 'is_processing', 'created_at').order_by("-created_at")
    queries = RedditPostFinderQuerySerializer(reddit_post_finder_queries, many=True).data
    return JsonResponse(status=200,
        data={'success': True,
              'queries': queries,
              'rpf_queries_generated': user.rpf_queries_generated,
              'max_rpf_allowed':max_rpf_allowed,
            })


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def get_detail_reddit_post_finder_queries(request: Request):
    """
    View to fetch RedditPostFinderResult details along with Hypestat data based on 'id' query parameter.
    """
    user: User = request.user
    website = user.current_active_website
    query_id = request.query_params.get('id')
    if not query_id:
        return JsonResponse({'success': False, 'message': "'id' query parameter is required."}, status=400)

    results = RedditPostFinderResult.objects.filter(
        reddit_post_finder__reddit_project_id=query_id,
        reddit_post_finder__website=website
    ).select_related('reddit_post_finder')

    if not results.exists():
        return JsonResponse({'success': False, 'message': 'No results found for this user and query ID.'}, status=404)

    # Serialize data
    serializer = RedditPostFinderResultSerializer(results, many=True)
    return JsonResponse(status=200, data={'success': True, 'queries': serializer.data})


@api_view(["POST"])
@permission_classes([IsAuthenticated])
def rename_keyword_project(request: Request):
    """
    API view to rename the keyword project name
    """
    user: User = request.user

    try:
        project_id = request.data['project_id']
        new_project_name = request.data['project_name']
    except KeyError as key:
        return JsonResponseBadRequest({'err_id': "MISSING_REQUIRED_KEY", 'message': f"Missing required key -> {key}."})

    if not new_project_name:
        return JsonResponseBadRequest({'err_id': "INVALID_PROJECT_NAME", 'message': f"Please provide a valid project name."})

    if not (5 <= len(new_project_name) <= 60):
        return JsonResponseBadRequest({'err_id': "INVALID_PROJECT_NAME_LENGTH",
                                       'message': "Project name must be between 5-60 characters long."})

    try:
        keyword_project: KeywordProject = user.keyword_projects.get(project_id=project_id)
    except KeywordProject.DoesNotExist:
        return JsonResponseBadRequest({'err_id': "KEYWORD_PROJECT_NOT_FOUND",
                                       'message': f"No keyword project exists with '{keyword_project}' ID"})

    # Update keyword project name
    keyword_project.project_name = new_project_name
    keyword_project.save()

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_article_language_preference(request: Request):
    """
    Returns the article language preference of the authenticated user.
    """
    user: User = request.user
    return JsonResponse({"article_language_preference": user.article_language_preference, "article_context": user.article_context}, status=200)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def delete_website_scan_data(request: Request):
    """
    Delete a webpage from the website.

    :param request: Django Rest Framework's Request object.
    :param url: Webpage URL to delete
    """
    user: User = request.user
    try:
        url: str = request.data['url']
    except KeyError:
        return JsonResponseKeyError()

    website: Website = user.current_active_website

    if not website:
        return JsonResponse(status=200, data={"web_pages": []})

    try:
        webpage = WebPage.objects.get(website=website, url=url)
        webpage.delete()  # Deletes the webpage object
        return JsonResponse(status=200, data={"message": "Webpage deleted successfully."})
    except WebPage.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Webpage not found."})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def update_webpage_summary(request: Request):
    """
    API to update the summary of an webpage.

    :param request: Django Request object containing `summary` and `url` in the request data.
    :return: JsonResponse with success or error message.
    """
    user: User = request.user
    try:
        summary: str = request.data.get('summary')
        schema: str = request.data.get('schema')
        url: str = request.data['url']
    except KeyError:
        return JsonResponseKeyError()

    website: Website = user.current_active_website

    if not website:
        return JsonResponse(status=200, data={"web_pages": []})

    try:
        webpage = WebPage.objects.get(website=website, url=url)
    except WebPage.DoesNotExist:
        return JsonResponse(status=404, data={"message": "Webpage not found."})

    if summary:
        webpage.summary = summary

    if schema:
        webpage.schema = schema

    webpage.save()

    return JsonResponse(status=200, data={"message": "Webpage summary updated successfully."})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_k8_job_status(request: Request):
    """
    This API retrieves the status of a Kubernetes job based on the provided `job_id`.

    :param request: Django Request object containing `summary` and `url` in the request data.
    :return: JsonResponse with success or error message.
    """
    job_id = request.query_params['job_id']

    try:
        job = KubernetesJob.objects.get(job_id=job_id)
        return JsonResponse({'status': job.status})
    except KubernetesJob.DoesNotExist:
        return JsonResponse({'status': 'not found'}, status=404)



@api_view(['POST'])
@permission_classes([IsAuthenticated])
def retry_stuck_failed_website_scanning_task(request: Request):
    """
    API view to retry stuck/failed website scanning task.
    :param request: Django Rest Framework's Request object.
    """
    try:
        user_id: int = request.data['user_id']
        website_domain: str = request.data['website_domain']
    except KeyError as k:
        logger.error(f"Missing key {k}")
        return JsonResponseKeyError()

    try:
        user = User.objects.get(id=user_id)
    except User.DoesNotExist:
        logger.error(f"No user found with {user_id} ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_USER_FOUND", 'message': f"No user found with {user_id} ID."})

    try:
        website = Website.objects.get(domain=website_domain)
    except Website.DoesNotExist:
        logger.error(f"No website found with {website_domain} domain.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_FOUND", 'message': f"No website found with {website_domain} domain."})

    if not user.website_set.all().filter(domain=website.domain).exists():
        logger.critical(f"{website.domain} is not connected to {user.email} user account.")
        return JsonResponseBadRequest(additional_data={'err_id': "WEBSITE_IS_NOT_CONNECTED",
                                                       'message': f"{website.domain} is not connected to {user.email} user account."})

    if website.is_crawled or website.task_queued:
        return JsonResponseBadRequest(additional_data={'err_id': "ALREADY_CRAWLED",
                                                       'message': "Website is already crawled."})

    if not website.pages_scan_limit_reached:
        # Mark the website as not crawled
        website.is_crawling = False
        website.is_crawled = False
        website.is_failed = False
        website.save()

        # Delegate the task to celery
        celery_start_website_scanning.delay(website.domain)

    else:
        # Mark the website as crawled
        website.is_crawling = False
        website.is_failed = False
        website.task_queued = False
        website.is_crawled = True
        website.crawling_ends_on = timezone.now()
        website.save()

    return JsonResponse(status=200, data={'message': "OK"})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def save_context_in_settings(request):
    """
    Save the context for article generation in settings.
    """
    user = request.user
    website = user.current_active_website

    if not website:
        return JsonResponse({"error": "No active website found."}, status=400)

    context = request.data.get('context', '').strip()
    old_context = request.data.get('old_context', '').strip()

    try:
        updated_count = InstructionAndContext.objects.filter(website=website, context=old_context).update(
        context=context,
        created_on=timezone.now()
        )

        if updated_count > 0:
            logger.info(f"Updated {updated_count} Instruction Context(s)")
            return JsonResponse({"message": f"{updated_count} context(s) updated successfully."}, status=200)

        # If no InstructionAndContext found, check and update article contexts
        article_qs = user.articles.filter(context=old_context).exclude(context__isnull=True, context="")
        article_updated_count = article_qs.update(
            context=context,
            created_on=timezone.now()
        )

        if article_updated_count > 0:
            return JsonResponse({"message": f"{article_updated_count} article context(s) updated successfully."}, status=200)

        # If nothing found, create new context
        InstructionAndContext.objects.create(website=website, context=context)

        return JsonResponse({"message": "Context saved successfully."}, status=201)

    except Exception as e:
        logger.error(f"Error saving context: {str(e)}")
        return JsonResponse({"error": "Failed to save context."}, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_latest_contexts(request):
    """
    Get the latest 10 contexts from InstructionAndContext and Article,
    sorted by created_on in descending order, returning only the context values.
    """

    user = request.user
    website = user.current_active_website

    instruction_contexts = InstructionAndContext.objects.filter(
        website=website
    ).order_by('-created_on')

    article_contexts = user.articles.filter().exclude(context__isnull=True).exclude(context="").order_by('-created_on')[:10]

    combined_contexts = sorted(
        list(instruction_contexts) + list(article_contexts),
        key=lambda obj: obj.created_on,
        reverse=True
    )

    unique_contexts = list(OrderedDict.fromkeys(obj.context for obj in combined_contexts if obj.context))

    return JsonResponse({"contexts": unique_contexts})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def generate_html_calculator_code(request: Request):
    """
    API view to generate new HTML calculator code.
    :param request: Django Rest Framework's Request object.
    """
    # Get the user & current active website
    user: User = request.user
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_IS_CONNECTED"})



    # Check user plan and calculator limits
    try:
        current_plan_data = get_stripe_product_data(user)
        calculator_limit = current_plan_data['metadata']['calculators']

    except stripe.error.InvalidRequestError:
        calculator_limit = -1  # Unlimited with branding for Trial

    # Check if user has reached their calculator limit (only for plans with limits)
    if calculator_limit != -1 and user.ai_calculators_generated >= calculator_limit:
        return JsonResponse(
            status=200,
            data={
                'status': "rejected",
                'reason': 'max_calculator_limit_reached',
                'limit': calculator_limit,
                'current_usage': user.ai_calculators_generated,
                'message': f"You have reached your calculator limit of {calculator_limit}. Upgrade your plan for more calculators."
            }
        )

    try:
        calculator_type: str = request.data['calc_type']
    except KeyError as key:
        logger.error(f"Missing required key: {key}")
        return JsonResponseKeyError()

    calculator_description = request.data.get('calc_description', '')

    # Define maximum token limit
    MAX_CALC_TYPE_TOKEN_LIMIT = 5000
    MAX_DESCRIPTION_TOKEN_LIMIT = 10000

    # Limit tokens if needed
    calculator_type = limit_tokens(calculator_type, MAX_CALC_TYPE_TOKEN_LIMIT)
    calculator_description = limit_tokens(calculator_description, MAX_DESCRIPTION_TOKEN_LIMIT)

    try:
        # Generate a unique calculator ID
        structured_calculator_type = calculator_type.lower().replace(' ', '-')
        calculator_id = f"{structured_calculator_type}-{secrets.token_hex(6)}"

        # Create the calculator instance first (empty, will be populated by worker)
        ai_calculator = AICalculator(
            calculator_id=calculator_id,
            website=website,
            calc_type=structured_calculator_type,
            calc_description=calculator_description,
            conversation=json.dumps([])  # Empty conversation initially
        )
        ai_calculator.save()

        # Submit the task directly to K8s or Fly.io based on DEBUG setting
        submission_result = create_ai_calculator_task(
            user=user,
            website=website,
            ai_calculator=ai_calculator
        )

        if submission_result['status'] == 'error':
            # If submission failed, delete the calculator instance and return error
            ai_calculator.delete()
            return JsonResponse(
                status=500,
                data={
                    'status': 'error',
                    'message': submission_result['message']
                }
            )

        if not DEBUG:
            # Delegate provisioning task to celery
            celery_check_flyio_provisioning.delay(submission_result['machine_id'],
                                                  submission_result['job_id'],
                                                  FLY_AI_CALCULATOR_APP_NAME,
                                                  FLY_AI_CALCULATOR_DEPLOY_TOKEN)

        # Increment user's calculator count only if submission was successful
        user.ai_calculators_generated += 1
        user.total_ai_calculators_generated += 1
        user.save()

        return JsonResponse({
            'status': 'submitted',
            'calculator_id': calculator_id,
            'calc_type': calculator_type,
            'calc_description': calculator_description,
            'message': submission_result['message'],
            'job_id': submission_result['job_id']
        })

    except Exception as err:
        logger.critical(f"Error queuing calculator generation: {err}")
        return JsonResponse(
            status=500,
            data={'status': "error", 'message': "Something went wrong, please try again later."}
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def modify_html_calculator_code(request: Request):
    """
    API view to modify existing HTML calculator code.
    :param request: Django Rest Framework's Request object.
    """
    # Define maximum token limit
    MAX_TOKEN_LIMIT = 10000

    # Get the user & current active website
    user: User = request.user
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_IS_CONNECTED"})

    try:
        calculator_id = request.data['calculator_id']
        modifications_instruction = request.data['modifications']
    except KeyError as key:
        logger.error(f"Missing required Key: {key}")
        return JsonResponseKeyError()

    # Get the calculator instance
    try:
        ai_calculator: AICalculator = user.ai_calculators.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        logger.error(f"No calculator exists with '{calculator_id}' project ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Limit modifications instruction tokens
    modifications_instruction = limit_tokens(modifications_instruction, MAX_TOKEN_LIMIT)

    try:
        # Update calculator status to modifying
        ai_calculator.generation_status = 'modifying'
        ai_calculator.save()

        # Submit modification task directly to K8s or Fly.io based on DEBUG setting
        submission_result = create_ai_calculator_task(
            user=user,
            website=website,
            ai_calculator=ai_calculator,
            modifications_instruction=modifications_instruction
        )

        if submission_result['status'] == 'error':
            # Reset status back to completed if submission failed
            ai_calculator.generation_status = 'completed'
            ai_calculator.save()
            return JsonResponse(
                status=500,
                data={
                    'status': 'error',
                    'message': submission_result['message']
                }
            )

        if not DEBUG:
            # Delegate provisioning task to celery
            celery_check_flyio_provisioning.delay(submission_result['machine_id'],
                                                  submission_result['job_id'],
                                                  FLY_AI_CALCULATOR_APP_NAME,
                                                  FLY_AI_CALCULATOR_DEPLOY_TOKEN)

        return JsonResponse({
            'status': 'submitted',
            'calculator_id': calculator_id,
            'modifications_instruction': modifications_instruction,
            'message': submission_result['message'],
            'job_id': submission_result['job_id']
        })

    except Exception as err:
        logger.critical(f"Error queuing calculator modification: {err}")
        return JsonResponse(
            status=500,
            data={'status': "error", 'message': "Something went wrong, please try again later."}
        )


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_ai_calculators(request: Request):
    """
    API view to fetch the Generated AI Calculators
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_IS_CONNECTED"})

    serializer = AICalculatorSerializer(user.ai_calculators.all().order_by('-created_on'), many=True)
    return JsonResponse(status=200, data={"table_data": serializer.data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_ai_calculator_data(request: Request):
    """
    API view to fetch the Generated AI Calculator Data
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_IS_CONNECTED"})

    try:
        calculator_id: str = request.query_params['calculator_id']
    except KeyError:
        return JsonResponseKeyError()

    try:
        ai_calculator: AICalculator = user.ai_calculators.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        logger.error(f"No calculator exists with '{calculator_id}' project ID.")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Get the AI Calculator versions
    ai_calculator_versions: QuerySet[AICalculatorVersion] = ai_calculator.aicalculatorversion_set.all().order_by('-created_on')

    calculator_data = {
        'calculator_id': ai_calculator.calculator_id,
        'calc_type': ai_calculator.calc_type,
        'calc_description': ai_calculator.calc_description or None,
        'code': ai_calculator.html_code,
        'script_tag': ai_calculator.script_tag,
        'generation_status': ai_calculator.generation_status,
        'versions': [
            {
                "id": version.id,
                "version_name": version.version_name,
                "html_code": version.html_code,
                "created_on": get_relative_time(version.created_on)
            } for version in ai_calculator_versions
        ]
    }

    return JsonResponse(status=200, data={"calculator_data": calculator_data})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def generate_calculator_script_tag(request: Request):
    """
    API view to generate a customized JavaScript script tag for a specific calculator.
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website | None = user.current_active_website

    if not website:
        return JsonResponseBadRequest(additional_data={'err_id': "NO_WEBSITE_CONNECTED"})

    try:
        calculator_id = request.query_params['calculator_id']
    except KeyError:
        return JsonResponseKeyError()

    try:
        # Fetch the calculator from db
        ai_calculator: AICalculator = user.ai_calculators.get(calculator_id=calculator_id)
    except AICalculator.DoesNotExist:
        logger.error(f"No calculator found with {calculator_id}")
        return JsonResponseBadRequest(additional_data={'err_id': "NO_CALCULATOR_FOUND"})

    # Get the server url
    server_url = request.build_absolute_uri('/')[:-1].replace('http://', 'https://')

    # encrypt the data
    script_data = {
        'CALCULATOR_ID': ai_calculator.calculator_id,
        'SERVER_URL': server_url,
        'BUTTON_COLOR': "007bff",
        'WEBSITE_NAME': website.name,
        'CALCULATOR_TITLE': ai_calculator.calc_type,
    }

    # Save the URL restriction and Script data
    ai_calculator.script_data = json.dumps(script_data)
    ai_calculator.save()

    # Refresh the instance
    ai_calculator.refresh_from_db()

    # Get the script tag
    script_tag = ai_calculator.script_tag

    return JsonResponse(data={'success': True, 'script_tag': script_tag})


@permission_classes([IsAuthenticated])
@api_view(["GET"])
def generate_ai_streaming_token(request: Request):
    """
    Generate an token for AI streaming
    :param request: Django Rest Framework's Request object
    """
    user: User = request.user
    current_datetime: datetime.datetime = datetime.datetime.now(tz=ZoneInfo("UTC"))

    # Create a unique, one-time streaming token
    unique_token = str(uuid.uuid4())

    AIStreamingToken.objects.create(
        token=unique_token,
        user=user,
        created_at=current_datetime,
        # set an expiry
        expires_at=current_datetime + datetime.timedelta(minutes=5)
    )

    return JsonResponse(status=200, data={
        "success": True,
        "token": unique_token
    })


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def scheduled_calendar_article_api(request: Request):
    """
    Returns paginated data for current website's article schedule.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    # Get pagination and filter parameters
    page = int(request.GET.get('page', 1))
    per_page = int(request.GET.get('per_page', 10))
    search = request.GET.get('search', '')
    sort = request.GET.get('sort', '')

    # Base queryset with optimizations
    queryset = (user.articles
        .select_related('keyword', 'schedulearticleposting')
        .prefetch_related('keyword__keywordproject_set')
        .annotate(
            keyword_project_id=Subquery(
                KeywordProject.objects
                .filter(
                    website=OuterRef('website'),
                    keywords=OuterRef('keyword_id')
                )
                .values('project_id')[:1]
            )
        )
        .filter(
            is_generated=True,
            is_processing=False,
            is_archived=False
        )
    )

    # Apply search if provided
    if search:
        queryset = queryset.filter(
            Q(title__icontains=search) |
            Q(keyword__keyword__icontains=search)
        )

    # Apply sorting
    if sort:
        sort_fields = []
        for sort_item in sort.split(','):
            field, direction = sort_item.split(':')
            field_mapping = {
                'articleTitle': 'article_title',
                'keyword': 'keyword__keyword',
                'wordCount': 'word_count',
                'date': 'created_on',
                'generatedOn': 'generated_on',
                'postedOn': 'posted_on',
                'scheduledOn': 'schedulearticleposting__schedule_on'
            }
            db_field = field_mapping.get(field, field)
            if direction == 'desc':
                db_field = f'-{db_field}'
            sort_fields.append(db_field)

        if sort_fields:
            queryset = queryset.order_by(*sort_fields)
    else:
        # Default sorting: Exclude processing articles
        queryset = queryset.order_by(F('generated_on').desc(nulls_last=True))

    # Get total count before pagination
    total_count = queryset.count()

    # Apply pagination
    start = (page - 1) * per_page
    end = start + per_page
    paginated_queryset = queryset[start:end]

    serialized_data = ArticleTitleTableDataSerializer(paginated_queryset, many=True).data

    response_data = {
        'title_data': serialized_data,
        'total': total_count,
        'all_integrations_with_unique_id': user.all_integrations_with_unique_id,
        'all_integrations' : user.all_integrations,
    }

    return JsonResponse(response_data, status=200)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@transaction.atomic
def delete_scheduled_calendar_article(request):
    """
    Deletes a scheduled article based on the article UID.
    :param request: Django Rest Framework's Request object.
    """
    user = request.user

    try:
        article_uid = request.data['article_uid']
    except KeyError:
        return JsonResponse({'success': False, 'message': "Article UID is required."}, status=400)

    try:
        article = user.articles.get(article_uid=article_uid)
    except Article.DoesNotExist:
        return JsonResponse({'success': False, 'message': f"No article found with UID '{article_uid}'."}, status=404)

    if not article.user.email == user.email:
        logger.critical(f"Article UID '{article_uid}' belongs to user '{article.user.email}', but '{user.email}' tried to delete it.")
        return JsonResponse({'success': False, 'message': "You do not have permission to delete this article."}, status=403)

    # Check if the article has been scheduled for publishing
    try:
        schedule_article_posting = ScheduleArticlePosting.objects.get(article=article)
        schedule_article_posting.delete()  # Delete the schedule entry
    except ScheduleArticlePosting.DoesNotExist:
        return JsonResponse({'success': False, 'message': "This article is not scheduled for publishing."}, status=404)

    return JsonResponse({'success': True, 'message': "Scheduled article deleted successfully."}, status=200)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def competitors_api_v2(request: Request):
    """
    Fetches list of competitors for website using crewai. Returns List of {"name", "url"}.

    :param request: Django Rest Framework's Request object
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        return JsonResponseBadRequest(additional_data={"err_id": "TRIAL_PLAN"})

    if request.method == "POST":
        try:
            domain: str = request.data['domain']
            limit: str = request.data.get('limit', '200')
            regenerate_competitor = bool(request.data.get('regenerateCompetitor', False))

        except KeyError:
            return JsonResponseBadRequest()

        if not domain:
            return JsonResponse({"error": "Domain parameter is required."}, status=400)

        competitor = CompetitorFinder(user, domain, limit, regenerate_competitor)

        if DEBUG:
            competitor.create_competitor_finder_task_on_k8s()
        else:
            competitor.create_competitor_finder_task_on_flyio()

        return JsonResponse(status=200, data={'success': True, 'message': "OK" , 'job_id': competitor.competitor_finder_job_id}, safe=False)

    else:
        return JsonResponseBadRequest()


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def post_selected_gsc_domain(request):
    """
    API view to store the selected domain (from frontend) in the user's profile.
    :param request: Django Rest Framework's Request object.
    """
    user: User = request.user
    website: Website = user.current_active_website

    selected_domain = request.data.get('selected_domain','')

    if not selected_domain:
        return JsonResponseBadRequest(additional_data={
            'err_id': "SELECTED_DOMAIN_NOT_FOUND",
            'message': f"Selected Domain is Required"
        })

    # Store the selected domain in the user's website profile
    website.selected_gsc_domain = selected_domain
    website.save()

    return JsonResponse({"message": "Selected domain saved successfully"}, status=200)


@api_view(['GET'])
@permission_classes([AllowAny])
def ghl_oauth_callback(request):
    "Generate the access token and refresh token for ghl authentication"
    code = request.query_params.get('code')
    state = request.query_params.get('state')
    base_url = request.build_absolute_uri('/').replace('http://', 'https://')

    if not state:
        logger.error("State ID missing from session.")
        return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integrations?error=missing_state")

    if not code:
        logger.error("Authorization code missing in callback.")
        return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integrations?=error=auth_code_missing")


    # Exchange code for tokens
    token_url = "https://services.leadconnectorhq.com/oauth/token"
    payload = {
        "client_id": GHL_CLIENT_ID,
        "client_secret": GHL_CLIENT_SECRET,
        "redirect_uri": f"{base_url}oauth/callback",
        "grant_type": "authorization_code",
        "code": code,
    }

    try:
        response = requests.post(token_url, data=payload)

        if response.status_code == 200:
            token_data = response.json()
            access_token = token_data.get("access_token")
            refresh_token = token_data.get("refresh_token")

            if not access_token or not refresh_token:
                logger.error(f"Invalid token response: {token_data}")
                return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integrations?error=invalid_token_response")

            try:
                with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
                    redis_value = redis_connection.get(state)
                    task_data = json.loads(redis_value) if redis_value else None
            except Exception as e:
                logger.critical(f"Error accessing Redis or parsing data: {e}")
                task_data = None

            if not task_data:
                return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integrations?error=authorize_the_ghl")

            website_id = task_data.get("website_id")
            location_id = task_data.get("location_id")
            site_id = task_data.get("site_id")
            ghl_domain = task_data.get("ghl_domain")

            website = Website.objects.filter(id=website_id).first()

            existing_record = GHLIntegration.objects.filter(
                website=website,
                location_id=location_id,
                ).first()

            if existing_record:
                return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integrations?error=already_integrated")

            GHLIntegration.objects.create(
                website=website,
                location_id=location_id,
                site_id=site_id,
                ghl_domain=ghl_domain,
                access_token=access_token,
                refresh_token=refresh_token,
            )
            # Save tokens with website
            return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integration/ghl/success/")

        else:
            logger.error(f"Token exchange failed with status {response.status_code}: {response.text}")
            return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integrations?error=token_exchange_failed")

    except Exception as e:
        logger.critical(f"Request to GHL token endpoint failed: {str(e)}")
        return HttpResponseRedirect(f"{settings.WP_RETURN_URL_DOMAIN}/integrations?error=ghl_request_failed")


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def initiate_ghl_auth(request):
    """
    Generate GHL authorization URL for the authenticated user's website.
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return Response({"err_id": "No active website found for user"}, status=400)

    ghl_url = request.data.get("ghl_url")

    ghl_domain = request.data.get('ghl_domain',"").strip()

    if not ghl_domain.startswith(("http://", "https://")):
        ghl_domain = f"https://{ghl_domain}"

    parsed_url = urlparse(ghl_domain)
    ghl_domain = f"{parsed_url.scheme}://{parsed_url.netloc}"

    state = str(uuid.uuid4())
    pattern = r"/location/([^/]+)/blogs/site/([^/?]+)"
    match = re.search(pattern, ghl_url)

    if match:
        location_id = match.group(1)
        site_id = match.group(2)
    else:
        location_id = None
        site_id = None

    if not all([location_id, site_id, ghl_domain]):
        return JsonResponse({
            'success': False,
            "message": "Missing one or more required fields: location_id, site_id, or ghl_domain."
        }, status=400)

    base_url = request.build_absolute_uri('/').replace('http://', 'https://')

    ghl_obj = {
        "location_id": location_id,
        "site_id": site_id,
        "ghl_domain": ghl_domain,
        "website_id": website.id,
    }

    with get_redis_connection(db=REDIS_TASK_DATA_DB) as redis_connection:
        redis_connection.set(state, json.dumps(ghl_obj))
        redis_connection.expire(state, REDIS_ART_GEN_EXPIRY)

    auth_url = (
        f"https://marketplace.gohighlevel.com/oauth/chooselocation?"
        f"response_type=code&redirect_uri={base_url}oauth/callback"
        f"&client_id={settings.GHL_CLIENT_ID}"
        f"&scope=medias.write%20medias.readonly%20blogs/post.write%20blogs/post-update.write%20blogs/category.readonly%20blogs/author.readonly%20blogs/posts.readonly%20blogs/list.readonly"
        f"&state={state}"
    )
    return Response({"auth_url": auth_url})

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_ghl_sites(request: Request):
    """
    API to get GHL sites list
    params: Django Request object
    return: list of backlinks
    """
    user: User = request.user

    try:
        current_plan_name = get_stripe_product_data(user)['name']
    except stripe.error.InvalidRequestError:
        current_plan_name = 'Trial'

    if current_plan_name == 'Trial':
        logger.error("User is not allowed to use this API. User is on trial plan.")
        return JsonResponseBadRequest()

    serializer = GHLSitesSerializer(user.ghl_integrations.all(), many=True)
    return JsonResponse(status=200, data={"table_data": serializer.data})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_ghl_category(request:Request):
    """
    Creates a new category in GHL.
    :param label: Name of the category.
    :return: Response JSON or error message.
    """

    user: User = request.user

    label = request.data.get("label")
    site_id = request.data.get("ghl_site_id")
    url = "https://services.leadconnectorhq.com/blogs/categories"

    ghl_integration = user.ghl_integrations.filter(site_id=site_id).first()

    if not ghl_integration:
        return JsonResponse({'success': False, "message": "GHL integration not found"})

    headers = {
        "Authorization": f"Bearer {ghl_integration.access_token}",
        "Version": "2021-07-28",
        "Content-Type": "application/json",
    }

    data = {
        "label": label,
        "urlSlug": label,
        "locationId": ghl_integration.location_id,
    }

    try:
        response = requests.post(url, json=data, headers=headers)
        response_data = response.json()

        if response.status_code in [200, 201]:
            return JsonResponse({'success': True, "data": response_data})

        elif response.status_code == 401:

            if refresh_ghl_token(ghl_integration):
                headers["Authorization"] = f"Bearer {ghl_integration.access_token}"

                response = requests.post(url, json=data, headers=headers)
                if response.status_code in [200, 201]:
                    return JsonResponse({'success': True, "data": response_data})

        else:
            return JsonResponse({'success': False, "message": response_data})

    except requests.RequestException as e:
        return JsonResponse({'success': False, "message": str(e)})

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def get_ghl_categories(request:Request):
    """
    Fetches blog categories from GHL.
    :return: List of categories or an error message.
    """
    user: User = request.user

    site_id = request.data.get("ghl_site_id")

    ghl_integration = user.ghl_integrations.filter(ghl_domain=site_id).first()

    if not ghl_integration:
        return JsonResponse(status=400, data={"message": "GHL integration not found", "success": False})
    
    url = "https://services.leadconnectorhq.com/blogs/categories"
    headers = {
        "Authorization": f"Bearer {ghl_integration.access_token}",
        "Version": "2021-07-28",
        "Accept": "application/json"
    }
    params = {"locationId": ghl_integration.location_id, "limit": 10, "offset": 0}

    try:
        response = requests.get(url, headers=headers, params=params)
        response_data = response.json()

        if response.status_code == 200 and "categories" in response_data:
            serializer = GHLCategoriesSerializer(response_data["categories"], many=True)
            return JsonResponse({'success': True, "categories": serializer.data})
        elif response.status_code == 401:
            if refresh_ghl_token(ghl_integration):
                headers["Authorization"] = f"Bearer {ghl_integration.access_token}"
                response = requests.get(url, headers=headers, params=params)
                if response.status_code == 200 and "categories" in response_data:
                    serializer = GHLCategoriesSerializer(response_data["categories"], many=True)
                    return JsonResponse({'success': True, "categories": serializer.data})
        else:
            return JsonResponse({'success': False, "message": response_data})
    except requests.RequestException as e:
        return JsonResponse({'success': False, "message": str(e)})


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def ghl_get_category_checked(request: Request):
    """
    Retrieve the category for a given article title.
    """
    try:
        article_title = request.query_params.get('article_title','')

        # Try to find the existing entry for the given article_title
        existing_category = GHLCategories.objects.filter(article_title=article_title).first()

        if existing_category:
            # If an entry is found, return the category details
            return JsonResponse({'success': True, 'message': 'Category found', 'category_id': existing_category.category_id}, status=200)

        return JsonResponse({'success': True, 'message': 'Category Default', 'category_id': 1}, status=200)

    except Exception as e:
        return JsonResponse({'success': False, 'message': str(e)}, status=500)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def trigger_fetch_wp_articles(request: Request):
    '''
    Fetch Wordpress published articles
    '''
    user = request.user
    website = user.current_active_website

    wp_integration = WordpressIntegration.objects.filter(website=website).first()
    if not wp_integration:
        return JsonResponse({"status": "error", "err_id": "NO_WP_INTEGRATION", "error_message": "No WordPress integration found."}, status=400)

    # credentials = google_integration_utils.get_google_oauth2_credentials(user, "google-search-console")
    # if not credentials:
    #     return JsonResponse({"status": "error", "err_id": "NO_GSC_INTEGRATION", "error_message": "GSC not integrated."}, status=400)

    try:
        task = celery_fetch_wp_published_articles_task.delay(user.id)
        return JsonResponse({
            "message": "WordPress article fetching task started.",
            "task_id": task.id
        }, status=202)
    except Exception as e:
        logger.critical(f"Error triggering task: {e}")
        return JsonResponse({
            "error": "Failed to start task",
            "detail": str(e)
        }, status=500)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_published_article(request: Request):
    '''
    Get all save wordpress published articles
    '''
    user = request.user
    website = user.current_active_website
    page = int(request.GET.get('page', 1))
    per_page = int(request.GET.get('per_page', 10))
    search = request.GET.get('search', '')
    sort = request.GET.get('sort', '')

    wp_articles = WordpressPublishedArticle.objects.select_related("article").filter(website=website)

    # Apply search
    if search:
        wp_articles = wp_articles.filter(
            Q(title__icontains=search)
        )

    # Apply sorting
    if sort:
        sort_fields = []
        for sort_item in sort.split(','):
            try:
                field, direction = sort_item.split(':')
            except ValueError:
                continue  # skip malformed sort strings

            field = field.strip().lower()  # normalize
            field_mapping = {
                'articleTitle': 'title',
                'published_date': 'published_date',
                'ranking_position': 'gsc_position',
                'word_count': 'article__word_count',
                'internal_link_count': 'article__internal_link_count',
                'external_link_count': 'article__external_link_count',
                'create_date': 'created_on',
            }

            db_field = field_mapping.get(field, field)
            if direction == 'desc':
                db_field = f'-{db_field}'
            sort_fields.append(db_field)

        if sort_fields:
            wp_articles = wp_articles.order_by(*sort_fields)


    total = wp_articles.count()

    # Paginate
    start = (page - 1) * per_page
    end = start + per_page
    wp_articles = wp_articles[start:end]

    serializer = WordpressPublishedArticleSerializer(wp_articles, many=True)

    return JsonResponse({
        "articles": serializer.data,
        "total": total
    }, status=200)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def fetch_search_image(request: Request):
    user: User = request.user

    search_keyword = request.data.get("search_keyword","").strip()
    image_count = 10
    if not search_keyword:
        return JsonResponse({"status": "error","message": "search keyword missing"})

    template_photo_list = get_unsplash_images(search_keyword, image_count)
    template_photos_count = len(template_photo_list)

    # If images not found on unsplash then try to fetch it from pexels
    if not template_photos_count:
        template_photo_list = get_pexels_images(search_keyword, image_count)
        template_photos_count = len(template_photo_list)
    elif not template_photo_list[0]["url"]:
        template_photo_list = get_pexels_images(search_keyword, image_count)
        template_photos_count = len(template_photo_list)

    if template_photos_count:
        images =  [{"url": img["url"], "alt": img["alt"]} for img in template_photo_list if img.get("url")]
    else:
        images = []

    return JsonResponse({
        "status": "success",
        "images": images
    })

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def save_include_linking(request: Request):
    """
    Toggle the 'include_linking' setting for a specific webpage associated with the user's website.
    """
    user: User = request.user
    website = user.current_active_website

    url = request.data.get("url")
    toggle_data = request.data.get("switch")

    if url is None or toggle_data is None:
        return JsonResponse(
            status=400,
            data={"error": "'url' and 'switch' fields are required."}
        )

    try:
        webpage = WebPage.objects.get(website=website, url=url)
        webpage.include_linking = toggle_data
        webpage.save()
        return JsonResponse(status=200, data={"message": "Toggle changes saved!"})
    
    except Exception as e:
        logger.critical(f"Failed to update togglel {e}")
        return JsonResponse(
            status=500,
            data={"error": "An unexpected error occurred.", "details": str(e)}
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_tools_integration_script(request: Request):
    """
    Returns the HTML script tag for tools embedding.
    :params request: Django Rest Framework's Request object.
    """
    user: User = request.user
    websites: QuerySet[Website] = user.website_set.all()

    if not websites:
        return JsonResponse(status=400, data={"error": "No Website is found."})

    return JsonResponse(status=200, data={"script_tag": user.get_tools_integration_script(request)})


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def icp_to_kw_research(request: Request):
    """
    Generating Keywords from openai with icp.    
    """
    user: User = request.user
    website = user.current_active_website
    
    icp = request.data.get("icp")
    num_keywords = min(int(request.data.get("count", 10)), 1000)
    selected_location = request.data['selectedLocation']
    
    if not icp:
        return JsonResponse({"error": "ICP is required."}, status=400)

    try:
        project_name = "ICP-Keyword-" + icp[:15]
        # Create a new KeywordProject instance
        keyword_project = KeywordProject.objects.create(
            website=website,
            project_name=project_name,
            project_id=f"icp-{str(uuid.uuid4())[:12]}",
            location_iso_code=selected_location['country_iso_code'].lower(),
        )
            
        task = celery_generate_keyword_from_icp.delay(user.id, num_keywords, icp, keyword_project.project_id, selected_location)
        
        return JsonResponse(data={"success": True, 'id': keyword_project.project_id, "task_id" : task.id}, status=200)
    except Exception as e:
        logger.critical(f"Failed to generate keywords from icp: {e}")
        return JsonResponse(data={"success": False, "error": str(e)}, status=500)
    
    
@api_view(['POST'])
@permission_classes([IsAuthenticated])
def fetch_volume_for_kw(request: Request):
    """
    Fetch volume data for given keyword hash IDs and update keyword entries.
    """
    user: User = request.user
    website = user.current_active_website

    keyword_hash_ids = request.data.get('kw_id', [])
    selected_location = request.data.get('selectedLocation')

    try:    
        max_keywords_allowed: int = get_stripe_product_data(user)['metadata']['max_keywords']
        remaining = max_keywords_allowed - (user.keywords_generated or 0)
        if remaining <= 0:
            return JsonResponse(status=200, data={"status": "rejected", 'reason': "max_limit_reached", 'message': 'max limit reached'})
        if len(keyword_hash_ids) > remaining:
            keyword_hash_ids = keyword_hash_ids[:remaining]
    except Exception as err:
        logger.critical(f"ai_keywords_research_api() - Error while checking max limit for user {user.email}: {err}")
        return JsonResponseServerError()
    
    # Fetch keywords from DB using hash IDs
    keywords_qs = Keyword.objects.filter(keyword_md5_hash__in=keyword_hash_ids)
    keyword_list = [kw.keyword for kw in keywords_qs]

    # Helper: chunk keywords into manageable chunks
    def chunk_list(lst, n):
        for i in range(0, len(lst), n):
            yield lst[i:i + n]

    all_keyword_data = []

    for chunk in chunk_list(keyword_list, 1000):
        try:
            if len(chunk) <= 275:
                result = get_keyword_volume_data(chunk, selected_location['country_iso_code'])  # KeywordsEverywhere                
            else:
                result = get_keyword_volume_data_v2(chunk, selected_location['location_code'])  # DataForSEO
            if result:
                all_keyword_data.extend(result)
        except Exception as e:
            logger.critical(f"Error fetching keyword data for chunk: {e}")

    # Map keyword → volume data
    keyword_data_map = {
        d['keyword'].lower(): d for d in all_keyword_data if 'keyword' in d
    }

    updated_keywords = []

    for kw in keywords_qs:
        kw_data = keyword_data_map.get(kw.keyword.lower())
        if kw_data:
            cpc = kw_data['cpc']['value']
            cpc_currency = kw_data['cpc']['currency']
            competition_index = kw_data["competition"]
            formatted_cpc_value = Decimal(str(cpc)).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            formatted_paid_difficulty = Decimal(str(competition_index)).quantize(Decimal('0.00'), rounding=ROUND_HALF_UP)
            try:
                kw.volume = int(kw_data.get('vol', 0))
                kw.cpc_currency = cpc_currency
                kw.cpc_value = formatted_cpc_value
                kw.paid_difficulty = formatted_paid_difficulty
                kw.trend = kw_data.get('trend', [])
                kw.kw_volume = True
                updated_keywords.append(kw)
            except Exception as e:
                logger.critical(f"Error parsing data for keyword {kw.keyword}: {e}")

    # Save updates + deduct credits
    if updated_keywords:
        Keyword.objects.bulk_update(updated_keywords, ['volume', 'cpc_currency', 'cpc_value', 'paid_difficulty', 'trend', 'kw_volume'])
        user.keywords_generated += len(updated_keywords)
        user.total_keywords_generated += len(updated_keywords)
        user.save()

        # Update traffic volume in related keyword project (using first keyword)
        first_keyword = updated_keywords[0]
        keyword_project = KeywordProject.objects.filter(keywords=first_keyword).first()
        if keyword_project:
            total_volume = keyword_project.keywords.aggregate(total=Sum('volume'))['total'] or 0
            keyword_project.total_traffic_volume = total_volume
            keyword_project.save()
            
        
    return JsonResponse(data={
        "updated_count": len(updated_keywords),
        "status": "success",
        "message": f"Updated volume for {len(updated_keywords)} keywords."
    })


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def verify_tools_loading_script(request: Request):
    """
    Verify the tools loading script is added to the website.
    """
    user: User = request.user
    website: Website = user.current_active_website

    if not website:
        return JsonResponse(data={
            'success': False,
            'message': "No website found",
            'err_id': "NO_WEBSITE_FOUND"
        })

    # Check if website is accessible
    website_homepage_url = f"{website.protocol}://{website.domain}"

    try:
        res = requests.get(website_homepage_url, timeout=30)

        if res.status_code != 200:
            return JsonResponse(data={
                'success': False,
                'message': "Website not accessible",
                'err_id': "WEBSITE_NOT_ACCESSIBLE"
            })

    except requests.exceptions.ConnectionError:
        return JsonResponse(data={
            'success': False,
            'message': "Website not accessible",
            'err_id': "WEBSITE_NOT_ACCESSIBLE"
        })

    except requests.exceptions.Timeout:
        return JsonResponse(data={
            'success': False,
            'message': "Website not accessible",
            'err_id': "WEBSITE_NOT_ACCESSIBLE"
        })

    try:
        # Check if script tag is present
        soup = BeautifulSoup(res.text, 'html.parser')
        script_tag = next(
            (s for s in soup.find_all('script') if 'data-user-id' in s.attrs), None
        )

    except (TypeError, ValueError, AttributeError) as e:
        logger.error(f"Error parsing HTML: {e}")
        return JsonResponse(data={
            'success': False,
            'message': "Website not accessible",
            'err_id': "WEBSITE_NOT_ACCESSIBLE"
        })

    if not script_tag:
        return JsonResponse(data={
            'success': False,
            'message': "Script tag not found",
            'err_id': "SCRIPT_TAG_NOT_FOUND"
        })

    encrpted_user_email: str = script_tag['data-user-id']

    # Decrypt the user email
    key = base64.urlsafe_b64encode(hashlib.sha256(settings.SECRET_KEY.encode()).digest())
    f = Fernet(key)

    # Decrypt the user email
    try:
        user_email = f.decrypt(encrpted_user_email.encode()).decode()

    except InvalidToken:
        logger.error(f"Invalid user email token: {user_email}")
        return JsonResponse(data={
            'success': False,
            'message': "Script tag not found",
            'err_id': "INVALID_USER_EMAIL"
        })

    except Exception as e:
        logger.critical(f"Error decrypting user email: {str(e)}")
        return JsonResponse(data={
            'success': False,
            'message': "Script tag not found",
            'err_id': "INVALID_USER_EMAIL"
        })

    # Check if user email matches with the current active website user email
    if user_email != website.user.email:
        return JsonResponse(data={
            'success': False,
            'message': "Script tag not found",
            'err_id': "INVALID_USER_EMAIL"
        })

    website.tools_loading_script_verified = True
    website.save()

    return JsonResponse(data={'success': True}, status=200)
